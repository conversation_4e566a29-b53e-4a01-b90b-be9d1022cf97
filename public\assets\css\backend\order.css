/* 订单列表样式 */
.order-list-container {
    margin-top: 20px;
}

.order-item {
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
    padding: 15px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    position: relative;
}

.order-item.selected {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

.order-header {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.order-header-item {
    margin-right: 15px;
    font-weight: bold;
}

.order-body {
    display: flex;
    flex-wrap: wrap;
}

.order {
    flex: 2;
    min-width: 300px;
    margin-right: 20px;
}

.order-product {
    display: flex;
    margin-bottom: 15px;
}

.product-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border: 1px solid #eee;
    margin-right: 15px;
}

.product-info {
    flex: 1;
}

.product-info-row {
    display: flex;
    margin-bottom: 5px;
}

.product-info-label {
    width: 80px;
    color: #666;
}

.product-info-value {
    flex: 1;
}

.customer-info {
    flex: 1;
    min-width: 200px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.customer-info-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.customer-info-content {
    margin-bottom: 10px;
}

.order-status {
    margin-top: 15px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.order-status.completed {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
}

.order-status-label {
    font-weight: bold;
    margin-right: 5px;
}

.order-tracking {
    margin-top: 5px;
    font-size: 12px;
}

.order-tracking-number {
    font-weight: bold;
}

.order-actions {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
}

.order-actions .btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

.print-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    background-color: #f5f5f5;
    color: #999;
    margin-right: 5px;
    font-size: 12px;
}

.print-status.completed {
    background-color: #52c41a;
    color: #fff;
}

/* 复制按钮样式 */
.product-copy-btn {
    display: inline-block;
    margin-left: 5px;
    padding: 0 5px;
    background-color: #1890ff;
    color: #fff;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    line-height: 20px;
    transition: all 0.3s;
}

.product-copy-btn:hover {
    background-color: #40a9ff;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .order-body {
        flex-direction: column;
    }
    
    .order, .customer-info {
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
    }
}
