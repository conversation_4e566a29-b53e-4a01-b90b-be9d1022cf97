<?php

namespace app\admin\model;

use think\Model;


class Store extends Model
{
    // 表名
    protected $name = 'store';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'latest_money_log_status_text'
    ];

    // 获取最新打款记录状态文本
    public function getLatestMoneyLogStatusTextAttr($value, $data)
    {
        $latestMoneyLog = $this->latestMoneyLog;
        if ($latestMoneyLog) {
            return $latestMoneyLog->status == 1 ? '已打款' : '未打款';
        }
        return '无记录';
    }

    // 关联打款记录
    public function moneyLogs()
    {
        return $this->hasMany('StoreMoneyLog', 'store_id', 'id');
    }

    // 关联最新的一条打款记录
    public function latestMoneyLog()
    {
        return $this->hasOne(StoreMoneyLog::class, 'store_id', 'id')
                    ->bind(['status']);
    }
}
