<?php
/*
*Created By PhpStorm
*File:demo.php
*User:尊杨科技
*Date:2025/5/28
*/
define('WALLTECH_SERVER', 'http://qa.etowertech.com');
define('ACCESS_TOKEN', 'test5AdbzO5OEeOpvgAVXUFE0A');
define('SECRET_KEY', '79db9e5OEeOpvgAVXUFWSD');

function build_headers($method, $path, $acceptType='application/json'){
    $walltech_date=date(DATE_RFC7231,time()-60*60*8);
    $auth = $method."\n".$walltech_date."\n".$path;
    $hash=base64_encode(hash_hmac('sha1', $auth, SECRET_KEY, true));

    //echo $walltech_date."<br>".$auth."<br>".$hash."<br>";
    return array(   'Content-Type: application/json',
        'Accept: '.$acceptType,
        'X-WallTech-Date: '.$walltech_date,
        'Authorization: WallTech '.ACCESS_TOKEN.':'.$hash);
}

function send_request($method,$headers,$body){
    $ch = curl_init(WALLTECH_SERVER.$method);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    curl_setopt($ch, CURLOPT_HTTPHEADER,$headers);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
    return curl_exec($ch);
}

function create_order(){
    $method='/services/integration/shipper/orders';
    $headers=build_headers('POST', WALLTECH_SERVER.$method);
    $body='[{"referenceNo":"12345","recipientName":"Paul Allan","addressLine1":"123 A Street","city":" PORT BOTANY","state":"NSW","postcode":"2036","weight":0.45,"description":"plastic toy","invoiceValue":9.89},{"referenceNo":"12346","recipientName":"Paul Allan","addressLine1":"123 A Street","city":" PORT BOTANY","state":"NSW","postcode":"2036","weight":0.45,"description":"plastic toy","invoiceValue":9.89}]';

    return send_request($method,$headers,$body);
}

function print_label(){
    $method='/services/integration/shipper/labels';
    $headers=build_headers('POST', WALLTECH_SERVER.$method);
    $body='["12345","12346"]';

    return send_request($method,$headers,$body);
}

function forecast(){
    $method='/services/integration/shipper/manifests';
    $headers=build_headers('POST', WALLTECH_SERVER.$method);
    $body='["12345","12346"]';

    return send_request($method,$headers,$body);
}

function track(){
    $method='/services/integration/shipper/trackingEvents';
    $headers=build_headers('POST', WALLTECH_SERVER.$method);
    $body='["SHW1005282011420","SHW1005379011425"]';

    return send_request($method,$headers,$body);
}


function pod() {
    $method='/services/integration/shipper/proof-of-delivery/3LQ0008086010229';
    $headers=build_headers('GET', WALLTECH_SERVER.$method);
    return send_request_get($method,$headers);
}

echo create_order();
echo print_label();
echo forecast();