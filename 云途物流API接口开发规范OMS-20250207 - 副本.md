<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

# 物流API

# 接口开发规范（OMS 1.3.1

# 版）

**文档创建信息**

<table border="1" ><tr>
<td colspan="1" rowspan="1">名称</td>
<td colspan="1" rowspan="1">物流API 接口开发规范（OMS 版）</td>
</tr><tr>
<td colspan="1" rowspan="1">版号</td>
<td colspan="1" rowspan="1">V1.3.1 </td>
</tr><tr>
<td colspan="1" rowspan="1">最新日期</td>
<td colspan="1" rowspan="1">2025-02-07 </td>
</tr><tr>
<td colspan="1" rowspan="1">创建人</td>
<td colspan="1" rowspan="1">IT 部</td>
</tr></table>

<!-- 1/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

**文档修订记录**

<table border="1" ><tr>
<td colspan="1" rowspan="1">修改日期</td>
<td colspan="1" rowspan="1">修改的章节</td>
<td colspan="1" rowspan="1">修改类型</td>
<td colspan="1" rowspan="1">修改描述</td>
<td colspan="1" rowspan="1">修改人</td>
<td colspan="1" rowspan="1">版本号</td>
</tr><tr>
<td colspan="1" rowspan="1">2018-12-01 </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">创建</td>
<td colspan="1" rowspan="1">张昆</td>
<td colspan="1" rowspan="1">1.0.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2019-03-07 </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">A,M </td>
<td colspan="1" rowspan="1">创建，修改</td>
<td colspan="1" rowspan="1">张润锋</td>
<td colspan="1" rowspan="1">1.0.1 </td>
</tr><tr>
<td colspan="1" rowspan="1">2020-02-21 </td>
<td colspan="1" rowspan="1">2.16 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">增加查询末端派送商</td>
<td colspan="1" rowspan="1">韦文山</td>
<td colspan="1" rowspan="1">1.0.2 </td>
</tr><tr>
<td colspan="1" rowspan="1">2020-03-05 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">修改新增税号和邮箱</td>
<td colspan="1" rowspan="1">韦文山</td>
<td colspan="1" rowspan="1">1.0.3 </td>
</tr><tr>
<td colspan="1" rowspan="1">2020-04-25 </td>
<td colspan="1" rowspan="1">2.15 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">订单状态</td>
<td colspan="1" rowspan="1">张满玲</td>
<td colspan="1" rowspan="1">1.0.3.1 </td>
</tr><tr>
<td colspan="1" rowspan="1">2020-06-04 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">增加本位币总费用</td>
<td colspan="1" rowspan="1">陈松</td>
<td colspan="1" rowspan="1">1.0.4 </td>
</tr><tr>
<td colspan="1" rowspan="1">2020-11-09 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">增加收件人手机号，收<br>件人ID </td>
<td colspan="1" rowspan="1">张满玲</td>
<td colspan="1" rowspan="1">1.0.5 </td>
</tr><tr>
<td colspan="1" rowspan="1">2020-12-21 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">增加欧盟税号，修改增值税号、币种</td>
<td colspan="1" rowspan="1">张满玲</td>
<td colspan="1" rowspan="1">1.0.6 </td>
</tr><tr>
<td colspan="1" rowspan="1">2020-12-30 </td>
<td colspan="1" rowspan="1">2.15 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">增加查询全程轨迹</td>
<td colspan="1" rowspan="1">张满玲</td>
<td colspan="1" rowspan="1">1.0.7 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-03-01 </td>
<td colspan="1" rowspan="1">2.4 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">修改包裹类型描述</td>
<td colspan="1" rowspan="1">林雪</td>
<td colspan="1" rowspan="1">1.0.8 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-03-18 </td>
<td colspan="1" rowspan="1">2.8 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">新增计费重量字段、修改订单状态描述</td>
<td colspan="1" rowspan="1">林雪</td>
<td colspan="1" rowspan="1">1.0.9 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-04-19 </td>
<td colspan="1" rowspan="1">1.3 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">新增测试地址</td>
<td colspan="1" rowspan="1">张满玲</td>
<td colspan="1" rowspan="1">1.1.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-05-24 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">新增FBA 下单字段</td>
<td colspan="1" rowspan="1">张满玲</td>
<td colspan="1" rowspan="1">1.1.1 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-06-16 </td>
<td colspan="1" rowspan="1">2.17 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">新增IOSS 备案</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.1.2 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-06-16 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">下单增加IOSS 号，币种支持欧元EUR </td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.1.2 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-06-18 </td>
<td colspan="1" rowspan="1">2.17 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">更新字段lossCode </td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.1.3 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-06-25 </td>
<td colspan="1" rowspan="1">2.17 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">更新字段UnitPrice 说明</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.1.3 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-06-25 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">ExtraCode 等于V1 代表同意使用云途预缴IOSS 附加服务</td>
<td colspan="1" rowspan="1">陈川</td>
<td colspan="1" rowspan="1">1.1.4 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-06-30 </td>
<td colspan="1" rowspan="1">2.17 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">增加lossName 表示IOSS 号别名</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.1.5 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-07-12 </td>
<td colspan="1" rowspan="1">2.16 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">修正返回字段以及说明</td>
<td colspan="1" rowspan="1">陈建新</td>
<td colspan="1" rowspan="1">1.1.6 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-07-27 </td>
<td colspan="1" rowspan="1">2.5 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">查询跟踪号增加返回子单跟踪号信息</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.1.7 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-08-16 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M,A </td>
<td colspan="1" rowspan="1">FBA 材质／用途分成两个字段：<br>材质（InvoicePart）和用途(InvoiceUsage) </td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.1.8 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-08-16 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">申报币种英国支持EUR </td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.1.8 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-09-16 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">运单申请走欧盟税改IOSS 流程的订单，lossCode或OrderExtra 必须二者填一</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.1.9 </td>
</tr><tr>
<td colspan="1" rowspan="1">2021-09-16 </td>
<td colspan="1" rowspan="1">2.17 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">IOSS 号备案需在用户中心同意最新版本服务注意事项告<br>知书</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.1.9 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-03-02 </td>
<td colspan="1" rowspan="1">2.18 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">按单号订阅轨迹</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.2.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-03-02 </td>
<td colspan="1" rowspan="1">2.19 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">按单号取消订阅</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.2.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-03-02 </td>
<td colspan="1" rowspan="1">2.20 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">获取按订单号订阅轨迹的数据</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.2.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-03-02 </td>
<td colspan="1" rowspan="1">2.21 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">按运输方式订阅轨迹</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.2.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-03-02 </td>
<td colspan="1" rowspan="1">2.22 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">取消按运输方式订阅</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.2.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-03-02 </td>
<td colspan="1" rowspan="1">2.23 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">获取按运输方式订阅的数据</td>
<td colspan="1" rowspan="1">张金浩</td>
<td colspan="1" rowspan="1">1.2.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-03-03 </td>
<td colspan="1" rowspan="1">2.4 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">查询价格；增加非必填字段Origin 计费地点云途默认：YT-SZ</td>
<td colspan="1" rowspan="1">宋杰</td>
<td colspan="1" rowspan="1">1.2.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-03-03 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">运单申请增加参数<br>ReferencelD </td>
<td colspan="1" rowspan="1">宋杰</td>
<td colspan="1" rowspan="1">1.2.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-07-20 </td>
<td colspan="1" rowspan="1">2.15 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">新增轨迹节点代码＆轨迹节</td>
<td colspan="1" rowspan="1">宋杰</td>
<td colspan="1" rowspan="1">1.2.1 </td>
</tr></table>

<!-- 2/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 3/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

<table border="1" ><tr>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">点英文描述<br>TrackNodeCode、<br>TrackCodeDescription </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">2022-11-04 </td>
<td colspan="1" rowspan="1">2.15 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">新增尾程服务商信息、轨迹发生地点信息：<br>ProviderName、<br>ProviderTelephone、<br>ProviderSite、POD、<br>ProcessCountry、<br>ProcessProvince、<br>ProcessCity、<br>AbnormalReasons </td>
<td colspan="1" rowspan="1">宋杰</td>
<td colspan="1" rowspan="1">1.2.2 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-12-13 </td>
<td colspan="1" rowspan="1">2.15 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">更新字段<br>AbnormalReasons，新增轨迹异常原因代码<br>(AbnormalReasonCode) 、轨迹异常原因描述<br>(AbnormalReason) </td>
<td colspan="1" rowspan="1">贺天</td>
<td colspan="1" rowspan="1">1.2.3 </td>
</tr><tr>
<td colspan="1" rowspan="1">2022-12-13 </td>
<td colspan="1" rowspan="1">2.15 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">新增字段长度单位<br>(SizeUnits) </td>
<td colspan="1" rowspan="1">贺天</td>
<td colspan="1" rowspan="1">1.2.3 </td>
</tr><tr>
<td colspan="1" rowspan="1">2023-04-18 </td>
<td colspan="1" rowspan="1">2.17 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">新增IOSS 号备案字段信息：FileUrl、<br>lossStatus、lossNote</td>
<td colspan="1" rowspan="1">王佳俊</td>
<td colspan="1" rowspan="1">1.2.4 </td>
</tr><tr>
<td colspan="1" rowspan="1">2023-05-19 </td>
<td colspan="1" rowspan="1">2.24 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">增加作废IOSS 号接口</td>
<td colspan="1" rowspan="1">吴启平</td>
<td colspan="1" rowspan="1">1.2.5 </td>
</tr><tr>
<td colspan="1" rowspan="1">2023-11-1 </td>
<td colspan="1" rowspan="1">2.15 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">增加PODs 字段</td>
<td colspan="1" rowspan="1">陈建新</td>
<td colspan="1" rowspan="1">1.2.6 </td>
</tr><tr>
<td colspan="1" rowspan="1">2024-03-15 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">增加<br>DangerousGoodsType 字段</td>
<td colspan="1" rowspan="1">吴启平</td>
<td colspan="1" rowspan="1">1.2.7 </td>
</tr><tr>
<td colspan="1" rowspan="1">2024-05-23 </td>
<td colspan="1" rowspan="1">2.4 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">增加额外服务和保价费字段ExtraServicesList，</td>
<td colspan="1" rowspan="1">吴启平</td>
<td colspan="1" rowspan="1">1.2.8 </td>
</tr><tr>
<td colspan="1" rowspan="1">2024-05-23 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">InsuredFee<br>字段增加保价服务描述<br>ExtraCode </td>
<td colspan="1" rowspan="1">吴启平</td>
<td colspan="1" rowspan="1">1.2.8 </td>
</tr><tr>
<td colspan="1" rowspan="1">2024-09-25 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">发件人信息增加邮箱和<br>USCI 代码</td>
<td colspan="1" rowspan="1">吴启平</td>
<td colspan="1" rowspan="1">1.2.9 </td>
</tr></table>

<!-- 4/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

<table border="1" ><tr>
<td colspan="1" rowspan="1">2024-09-25 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">A </td>
<td colspan="1" rowspan="1">增加平台信息</td>
<td colspan="1" rowspan="1">吴启平</td>
<td colspan="1" rowspan="1">1.2.9 </td>
</tr><tr>
<td colspan="1" rowspan="1">2024-11-06 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">字段增加签名服务描述<br>ExtraCode </td>
<td colspan="1" rowspan="1">吴启平</td>
<td colspan="1" rowspan="1">1.3.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2024-11-06 </td>
<td colspan="1" rowspan="1">2.4 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">增加签名服务描述</td>
<td colspan="1" rowspan="1">吴启平</td>
<td colspan="1" rowspan="1">1.3.0 </td>
</tr><tr>
<td colspan="1" rowspan="1">2025-02-07 </td>
<td colspan="1" rowspan="1">2.7 </td>
<td colspan="1" rowspan="1">M </td>
<td colspan="1" rowspan="1">增加增值税号预缴服务描述</td>
<td colspan="1" rowspan="1">吴启平</td>
<td colspan="1" rowspan="1">1.3.1 </td>
</tr></table>

·修改类型分为A-ADDED（增加） M - MODIFIED（修改） D- DELETED（删除）

# 引言

术语和缩略词

<table border="1" ><tr>
<td colspan="1" rowspan="1">英文缩写</td>
<td colspan="1" rowspan="1">英文全称</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OMS </td>
<td colspan="1" rowspan="1">Logistics Manage Server </td>
<td colspan="1" rowspan="1">OMS 管理服务</td>
</tr><tr>
<td colspan="1" rowspan="1">Http(Https) </td>
<td colspan="1" rowspan="1">HypertextTransfer<br>Protocol </td>
<td colspan="1" rowspan="1">参见HTTP SPEC </td>
</tr><tr>
<td colspan="1" rowspan="1">测试账号</td>
<td colspan="1" rowspan="2"></td>
<td colspan="1" rowspan="2">测试客户编号为：ITC0893791<br>测试ApiSecret 为：<br>axzc2utvPbfc9UbJDOh+7w==<br>测试账户只能在测试地址使用<br>测试地址：http://omsapi.uat.yunexpress.com</td>
</tr><tr>
<td colspan="1" rowspan="1">ServerAddress </td>
</tr><tr>
<td colspan="1" rowspan="1">ServerAddress </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">正式地址：http://oms.api.yunexpress.com </td>
</tr></table>

<!-- 5/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

# 读者对象

本文档提供以下人员参考阅览：

开发人员；

设计人员；

<!-- 6/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

# 目录

1．接口通信协议．....................................................................8

1.1 通信步骤．.....................................................................8

1.2 通信数据内容及格式............................................................8

1.3用户认证．.....................................................................8

1.4 接口调用说明.................................................................8

1.5 云途API接入基本流程．...........................................................9

2．接口功能和协议．................................................................9

2.1 查询国家简码.................................................................9

2.2 查询运输方式.................................................................12

2.3 查询货品类型................................................................14

2.4 查询价格....................................................................16

2.5 查询跟踪号．..................................................................19

2.6 查询发件人信息..............................................................22

2.7 运单申请．....................................................................24

2.8 查询运单....................................................................34

2.9 修改订单预报重量.............................................................39

2.10 订单删除...................................................................41

2.11 订单拦截．...................................................................43

2.12 标签打印．...................................................................45

2.13 查询物流运费明细............................................................47

2.14 用户注册．...................................................................49

2.15 查询物流轨迹信息............................................................51

2.16 查询末端派送商..............................................................54

2.17 IOSS号备案．..................................................................57

2.18 按单号订阅轨迹．.............................................................59

2.19 按单号取消轨迹订阅..........................................................62

<!-- 7/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

2.20 获取按订单号订阅轨迹的数据....................................................65

2.21 按运输方式订阅轨迹．...........................................................68

2.22 取消按运输方式订阅．............................................................72

2.23 获取按运输方式订阅的数据．......................................................75

2.24 IOSS号作废．..................................................................78

3．附录．.............................................................................80

3.1 XML解析规范.....................................................................80

3.2 结果代码表．....................................................................80

<!-- 8/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

# 1．接口通信协议

## 1.1 通信步骤

1、客户端处理业务过程中向 OMS提交业务处理请求；

2、OMS根据客户的请求作相应的处理；

3、处理完毕后，OMS返回客户端处理结果响应信息。

## 1.2 通信数据内容及格式

客户端以URI＋参数形式提交数据请求，OMS 以JSON格式返回数据。具体的数据格式参照本文档的接口规格说明。请求和响应数据包均采用 UTF-8编码。

## 1.3 用户认证

获取用户认证Token

客户编号：客户新注册时由业务部门提供的客户身份唯一标识编号

ApiSecret：申请API接口服务时由业务部门提供的一个密钥

Token值：由客户编号和ApiSecret构成中间用”＆”分隔，并要Base64编码。

测试客户编号为：ITC0893791

测试ApiSecret为：axzc2utvPbfc9UbJDOh+7w=＝

由 'ITC0893791&axzc2utvPbfc9UbJDOh+7w==＇字符（不含单引号）通

Base64编码后产生的字符则为该用户的Token值

**1.4 接口调用说明**

**用户方每次发起http请求时，需要在http** header填入一个属性：

Authorization, Authorization 对应用户的认证码，也就是用户认证的Token。

Http requestheader信息如下表格所示：

<table border="1" ><tr>
<td colspan="1" rowspan="1">Http request header </td>
<td colspan="1" rowspan="1">默认值</td>
<td colspan="1" rowspan="1">描述</td>
</tr><tr>
<td colspan="1" rowspan="1">Accept </td>
<td colspan="1" rowspan="1">application/json</td>
<td colspan="1" rowspan="1">接口的返回类型</td>
</tr><tr>
<td colspan="1" rowspan="1">Authorization </td>
<td colspan="1" rowspan="1">Basic Token 值</td>
<td colspan="1" rowspan="1">用户认证的Token 值</td>
</tr></table>

例如：Authorization:Basic Token，注意：Basic 和 Token值之间要加空格。

<!-- 9/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

<table border="1" ><tr>
<td colspan="1" rowspan="1">KEY </td>
<td colspan="1" rowspan="1">VALUE </td>
</tr><tr>
<td colspan="1" rowspan="1">Authorization </td>
<td colspan="1" rowspan="1">Basic QzA2OTAxJjNWbmlBc2RPNm1VPQ==</td>
</tr><tr>
<td colspan="1" rowspan="1">Content-Type </td>
<td colspan="1" rowspan="1">application/json</td>
</tr><tr>
<td colspan="1" rowspan="1">charset </td>
<td colspan="1" rowspan="1">UTF-8 </td>
</tr><tr>
<td colspan="1" rowspan="1">Key </td>
<td colspan="1" rowspan="1">Value </td>
</tr></table>

## 1.5 云途API接入基本流程

A.预报／删除订单，获取云途运单号

预报运单接口：http://ADDR/api/WayBill/CreateOrder

B.删除运单（仅支持草稿、已预报运单）

删除运单接口：http://ADDR/api/WayBill/Delete

C.拦截运单（仅支持已预报、已入仓运单）

运单拦截接口：http://ADDR/api/WayBill/Intercept

D.云途运单标签打印（建议本地标签缓存）

标签打印接口：http://ADDR/api/Label/Print

E.揽收包裹预报（规划中，暂未开放；揽收暂联系业务员）

F.修改订单预报重量（仅支持已预报）

修改预报重量接口：http://ADDR/api/WayBi11/UpdateWeight

G.获取末端服务商跟踪号（建议轮询任务）

服务商单号获取接口：http://ADDR/api/Waybill/GetTrackingNumber

H.查询轨迹（依据轨迹订阅方式）与末端服务商

轨迹查询接口：http://ADDR/api/Tracking/GetTrackInfo

末端服务商查询接口：http://ADDR/api/Waybill/GetCarrier

# 2．接口功能和协议

## 2.1 查询国家简码

接口功能描述

客户端向OMS请求查询国家简码接口。

事件流

基本流：

A.客户端向OMS提交查询国家简码请求；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

C. OMS验证完信息后，以RETURN的形式返回响应数据。

接口地址

<!-- 1(/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

<u>http://ADDR/api/Common/GetCountry</u>

协议定义

**请求包字段说明**：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">无</td>
<td colspan="1" rowspan="1">无</td>
<td colspan="1" rowspan="1">无</td>
<td colspan="1" rowspan="1">无</td>
<td colspan="1" rowspan="1">无请求参数</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Items </td>
<td colspan="1" rowspan="1">Country[]</td>
<td colspan="1" rowspan="1">请求成功的国家简码</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

**Country类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">国家简码</td>
</tr><tr>
<td colspan="1" rowspan="1">EName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">国家英文名</td>
</tr><tr>
<td colspan="1" rowspan="1">CName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">国家中文名（去掉中文名）待定</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

消息示例

消息示例

**请求数据包示例：**

GET请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/Common/GetCountry</u>

成功响应数据包示例**：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Items": [{<br>"CountryCode":"AD",<br>"EName": "ANDORRA",<br>＂CName":＂安道尔”<br>},{<br>"CountryCode": "AE",<br>"EName": "UNITED ARAB EMIRATES",<br>＂CName ":＂阿拉伯联合酋长国”<br>},{<br>"CountryCode": "AF",<br>"EName": "AFGHANISTAN", </td>
</tr></table>

<!-- 10/ -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

失败响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Items": nul1,<br>"Code":"1006",<br>＂Message":＂未找到数据＂，<br>"RequestId": "5c8ba4db9f2d4ec4b9b197efd0167de1"<br>"TimeStamp": "2019-03-07T08:37:01.8200483+00:00"</td>
</tr></table>

<!-- 11/ -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

参考<u>【失败响应】</u>详细说明。

## 2.2 查询运输方式

接口功能描述

客户端向OMS请求查询运输方式接口。

事件流

基本流：

A.客户端向OMS提交查询运输方式请求；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据。

接口地址

<u>http://ADDR/api/Common/GetShippingMethods</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">5 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">国家简码，未填写国家代表查询所有运输方式</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Items </td>
<td colspan="1" rowspan="1">ShippingMethod[]</td>
<td colspan="1" rowspan="1">请求成功的运输方式</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

ShippingMethod 类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">运输方式代号</td>
</tr><tr>
<td colspan="1" rowspan="1">CName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">运输方式中文名称</td>
</tr><tr>
<td colspan="1" rowspan="1">EName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">运输方式英文名称</td>
</tr><tr>
<td colspan="1" rowspan="1">HasTrackingNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">是否带跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">DisplayName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">运输方式显示名称</td>
</tr></table>

# 消息示例

消息示例

**请求数据包示例：**

GET请求，数据类型请用json类型，请求数据为：

<!-- 12/ -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

<u>http://ADDR/api/Common/GetShippingMethods</u>（获取所有列表）

<u>http://ADDR/api/Common/GetShippingMethods?CountryCode=GB</u>

（GB代表具体国家简码）

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Items":[<br>"Code":"THPHR",<br>＂CName ":＂云途全球专线挂号（特惠普货）"，<br>"EName": "Yunexpress Global Driect Economical<br>Line(general)",<br>"HasTrackingNumber":true,<br>＂DisplayName ":＂云途全球专线挂号（特惠普货）”<br>},<br>{<br>"Code":"WISHTHZX",<br>＂CName":"wish邮云途全球专线挂号＂，<br>"EName":"Wish Yunexpress Global Economical Li<br>ne",<br>"HasTrackingNumber":true,<br>＂DisplayName":"wish邮云途全球专线挂号”<br>{<br>"Code": "JMZXR",<br>＂CName":"Joom邮云途专线挂号”，<br>"EName" : "Joom YunExpress Direct Line",<br>"HasTrackingNumber":true,<br>＂DisplayName":"Joom邮云途专线挂号＂<br>"Code" : "DEZXR",<br>＂CName ":＂德国专线挂号＂，<br>"EName":"YunExpress Germany Direct Line",<br>"HasTrackingNumber":true,<br>＂DisplayName":＂德国专线挂号”<br>{<br>"Code": "THZXR",<br>＂CName ":＂云途全球专线挂号（特惠带电）”，<br>"EName":"Yunexpress Global Driect Economical<br>Line",<br>"HasTrackingNumber":true,<br>＂DisplayName ":＂云途全球专线挂号（特惠带电）”</td>
</tr></table>

<!-- 13/ -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

**失败响应数据包示例：**

参考<u>【失败响应】</u>详细说明。

## 2.3 查询货品类型

接口功能描述

客户端向OMS请求查询货品类型接口。

事件流

基本流：

A.客户端向OMS提交查询货品类型请求；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

C. OMS验证完信息后，以RETURN的形式返回响应数据。

<!-- 14/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

接口地址

<u>http://ADDR/api/Common/GetGoodsType</u>

## 协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">无</td>
<td colspan="1" rowspan="1">无</td>
<td colspan="1" rowspan="1">无</td>
<td colspan="1" rowspan="1">无</td>
<td colspan="1" rowspan="1">无请求参数</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Items </td>
<td colspan="1" rowspan="1">GoodsType[]</td>
<td colspan="1" rowspan="1">请求成功的货品类型信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

**GoodsTyp 类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Id </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">货品类型 ID </td>
</tr><tr>
<td colspan="1" rowspan="1">CName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">货品类型中文名称</td>
</tr><tr>
<td colspan="1" rowspan="1">EName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">货品类型英文名称</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u><u>&nbsp;</u>详细说明。

消息示例

消息示例

**请求数据包示例：**

GET请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/Common/GetGoodstype</u>

### 成功响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Items":[{<br>"Id":2,<br>＂CName":＂文件”，<br>"EName": "Document"<br>},{<br>"Id":1,<br>＂CName":＂分单＂，</td>
</tr></table>

<!-- 15/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**失败响应数据包示例：**

参考<u>【失败响应】</u>详细说明。

### 2.4 查询价格

接口功能描述

客户端向OMS请求查询报价接口。

事件流

基本流：

A.客户端向OMS提交查询报价请求；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据。

接口地址

<u>http://ADDR/api/Freight/GetPriceTrial</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">5 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">国家简码</td>
</tr><tr>
<td colspan="1" rowspan="1">Weight </td>
<td colspan="1" rowspan="1">decimal(18,3) </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">包裹重量，单位kg ，支持3 位小数</td>
</tr><tr>
<td colspan="1" rowspan="1">Length </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">包裹长度，单位cm ，不带小数，不填写默认1 </td>
</tr><tr>
<td colspan="1" rowspan="1">Width </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">包裹宽度，单位cm ，不带小数，不填写默认1 </td>
</tr><tr>
<td colspan="1" rowspan="1">Height </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">包裹高度，单位cm ，不带小数，不填写默认1 </td>
</tr><tr>
<td colspan="1" rowspan="1">PostCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">邮编</td>
</tr><tr>
<td colspan="1" rowspan="1">PackageType </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">包裹类型，1 -带电，0 -普货，默认1 </td>
</tr><tr>
<td colspan="1" rowspan="1">Origin </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">计费地点（云途默认：YT -SZ </td>
</tr><tr>
<td colspan="1" rowspan="1">ExtraServicesLis t </td>
<td colspan="1" rowspan="1">array </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">额外服务列表，示例<br>ExtraServicesList[0]. ExtraServ ice=BJFDR</td>
</tr></table>

<!-- 16/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

ExtraServicesList类型

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">ExtraService </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">保价服务代码：<br>BJFA：固定金额<br>BJFR：运费＊费率<br>BJDR ：申报价值＊费率<br>BJFDR :（运费＋申报价值）＊费率；<br>Ls0091：签名服务</td>
</tr></table>

**请求成功响应包字段说明**：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Items </td>
<td colspan="1" rowspan="1">Quotation []</td>
<td colspan="1" rowspan="1">添加成功的订单信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">sting </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

**Quotation 类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">可服务的运输方式代码</td>
</tr><tr>
<td colspan="1" rowspan="1">CName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">可服务的运输方式中文名称</td>
</tr><tr>
<td colspan="1" rowspan="1">EName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">可服务的运输方式英文名称</td>
</tr><tr>
<td colspan="1" rowspan="1">ShippingFee </td>
<td colspan="1" rowspan="1">decimal(18,2) </td>
<td colspan="1" rowspan="1">基础运费</td>
</tr><tr>
<td colspan="1" rowspan="1">RegistrationFee </td>
<td colspan="1" rowspan="1">decimal(18,2) </td>
<td colspan="1" rowspan="1">挂号费</td>
</tr><tr>
<td colspan="1" rowspan="1">FuelFee </td>
<td colspan="1" rowspan="1">decimal(18,2) </td>
<td colspan="1" rowspan="1">燃油费</td>
</tr><tr>
<td colspan="1" rowspan="1">SundryFee </td>
<td colspan="1" rowspan="1">decimal(18,2) </td>
<td colspan="1" rowspan="1">杂费</td>
</tr><tr>
<td colspan="1" rowspan="1">TariffPrepayFee </td>
<td colspan="1" rowspan="1">decimal(18,2) </td>
<td colspan="1" rowspan="1">关税预付服务费</td>
</tr><tr>
<td colspan="1" rowspan="1">InsuredFee </td>
<td colspan="1" rowspan="1">decimal(18,2) </td>
<td colspan="1" rowspan="1">保价费</td>
</tr><tr>
<td colspan="1" rowspan="1">TotalFee </td>
<td colspan="1" rowspan="1">decimal(18,2) </td>
<td colspan="1" rowspan="1">总费用</td>
</tr><tr>
<td colspan="1" rowspan="1">DeliveryDays </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">预计时效</td>
</tr><tr>
<td colspan="1" rowspan="1">Remark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">说明（当Remark 为空时不显示）</td>
</tr><tr>
<td colspan="1" rowspan="1">SignatureFee </td>
<td colspan="1" rowspan="1">decimal(18,2) </td>
<td colspan="1" rowspan="1">签名服务费</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

消息示例

<!-- 17/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**请求数据包示例：**

GET请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/Freight/GetPriceTrial?CountryCode=GB&Weight=</u>

0.125&Length=1&Width=1&Height=1&PackageType=1

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Items":[<br>"Code":"PK0001",<br>＂CName ":＂香港小包挂号＂，<br>"EName":"Hong Kong Post Registered Air Mail",<br>"ShippingFee":0.13,<br>"RegistrationFee":0,<br>"FuelFee":0,<br>"SundryFee":0,<br>"TariffPrepayFee":nul1,<br>"TotalFee":0.13,<br>"DeliveryDays":nul1,<br>＂Remark":＂普货＂<br>{<br>"Code":"GBZXR",<br>＂CName ":＂英国专线标准”，<br>"EName":"YunExpress Britain Direct Line",<br>"ShippingFee":2.01,<br>"RegistrationFee":0,<br>"FuelFee":0,<br>"SundryFee":0,<br>"TariffPrepayFee":nul1,<br>"TotalFee":2.01,<br>"DeliveryDays":nul1,<br>"Remark":""<br>],<br>"Code":"0000",<br>＂Message":＂提交成功＂，<br>"RequestId": "d4bd87ef72d4445898c31e15550acee7",<br>"TimeStamp": "2019-03-07T02:45:46.0857173+00:00"</td>
</tr></table>

失败响应数据包示例：

参考<u>【失败响应】</u><u>&nbsp;</u>详细说明。

<!-- 18/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

### 2.5 查询跟踪号

接口功能描述

客户端向OMS请求查询跟踪号接口。（30分钟缓存）

事件流

基本流：

A.客户端向OMS提交查询跟踪号请求；

B.OMS验证客户端信息合法性：


![](https://web-api.textin.com/ocr_image/external/2d9502b8054b1ef5.jpg)

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据。

接口地址<u>http://ADDR/api/Waybill/GetTrackingNumber</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CustomerOrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">2000 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">客户订单号，多个以逗号分开</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Items </td>
<td colspan="1" rowspan="1">OrderInfo []</td>
<td colspan="1" rowspan="1">添加成功的订单信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

**OrderInfo类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CustomerOrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">订单号</td>
</tr><tr>
<td colspan="1" rowspan="1">TrackingNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">WayBillNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">运单号</td>
</tr><tr>
<td colspan="1" rowspan="1">ChildTrackings </td>
<td colspan="1" rowspan="1">ChildTrackingInfo[]</td>
<td colspan="1" rowspan="1">子单跟踪号信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Remark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">详情描述</td>
</tr><tr>
<td colspan="1" rowspan="1">Status </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">1 -跟踪号生成成功；2 -跟踪号生成中；3 -<br>跟踪号无需生成；4 -跟踪号生成失败</td>
</tr></table>

<!-- 19/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**ChildTrackingInfo类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">ChildOrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">子单号</td>
</tr><tr>
<td colspan="1" rowspan="1">ChildTrackingNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">子单跟踪号</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

消息示例

消息示例

**请求数据包示例：**

GET请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/Waybill/GetTrackingNumber?CustomerOrderNum</u>

<u>ber=test201812200001,wish20181205000012</u>

**成功响应数据包示例：**

{

"Items":[

{

**"OrderNumber"**:"",

"CustomerOrderNumber": "XX1193170958017490056",

"TrackingNumber":"",

"WayBil1Number": "YT211933101466682",

"ChildTrackings":[

{

"ChildOrderNumber": "YT2119331014000082U001",

"ChildTrackingNumber":nu11

"ChildOrderNumber":"YT2119331014000082U002",

"ChildTrackingNumber":nul1

<!-- 20/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<table border="1" ><tr>
<td colspan="1" rowspan="1">＂Remark":＂跟踪号生成中＂，<br>"Status":2<br>],<br>"Code":"0000",<br>＇Message":＂提交成功！”，<br>"RequestId": "OHMAGP6FJVU7P:00000002",<br>"TimeStamp": "2021-07-27T08:16:21.1837409+00:00"</td>
</tr></table>

**失败响应数据包示例：**

参考<u>【失败响应】</u>详细说明。

<!-- 21/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

### 2.6 查询发件人信息

接口功能描述

系统将分配好的发件人信息返回给客户

事件流

基本流：

A.客户端向OMS提交查询发件人信息请求；

B. OMS为订单分配可用的地址信息

·检查请求数据的格式

C.在OMS验证信息后，它以RETURN的形式返回响应数据。

接口地址

<u>http://ADDR/api/WVayBill/GetSender</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">2000 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">查询号码，可输入运单号、订单号、跟踪号</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Items </td>
<td colspan="1" rowspan="1">OrderSender </td>
<td colspan="1" rowspan="1">发件人信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码：0000 ：分配成功；1006 分配失败；1004：接口异常</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

<!-- 22/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

OrderSender **类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">客户输入的查询号</td>
</tr><tr>
<td colspan="1" rowspan="1">WayBillNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">运单号</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人国家简码</td>
</tr><tr>
<td colspan="1" rowspan="1">FirstName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人姓</td>
</tr><tr>
<td colspan="1" rowspan="1">LastName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人名</td>
</tr><tr>
<td colspan="1" rowspan="1">Company </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人公司</td>
</tr><tr>
<td colspan="1" rowspan="1">Address </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人地址</td>
</tr><tr>
<td colspan="1" rowspan="1">City </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人城市</td>
</tr><tr>
<td colspan="1" rowspan="1">State </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人省州</td>
</tr><tr>
<td colspan="1" rowspan="1">Zip </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人邮编</td>
</tr><tr>
<td colspan="1" rowspan="1">Phone </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人电话</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u><u>&nbsp;</u>详细说明。

消息示例

消息示例

**请求数据包示例：**

GET请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/WayBill/GetSender?OrderNumber=YT1908821203000021</u>

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Items":{<br>"OrderNumber": "YT1908821203000021",<br>"WayBil1Number": "YT1908821203000021",<br>"CountryCode":"BG",<br>"FirstName": "test01",<br>"LastName":"",<br>"Company": "body corporate01", </td>
</tr></table>

<!-- 23/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**失败响应数据包示例：**

参考<u>【失败响应】</u>详细说明。

### 2.7 运单申请

接口功能描述

客户端向OMS请求运单申请。

事件流

基本流：

A.客户端向OMS提交运单申请请求；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据。

接口地址 <u>http://ADDR/api/WayBill/CreateOrder</u>

<!-- 24/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CustomerOrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">客户订单号，不能重复</td>
</tr><tr>
<td colspan="1" rowspan="1">ShippingMethodCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">运输方式代码</td>
</tr><tr>
<td colspan="1" rowspan="1">TrackingNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">包裹跟踪号，可以不填写</td>
</tr><tr>
<td colspan="1" rowspan="1">TransactionNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">平台交易号（wish 邮）</td>
</tr><tr>
<td colspan="1" rowspan="1">TaxNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">增值税号，巴西国家必填CPF 或CNPJ ，<br>CPF 码格式为000.000.000-00,CNPJ 码格式为00.000.000/0000-00，其它国家非必填，<br>英国税号格式为：前缀GB +9 位纯数字 或者前缀GB +12 位纯数字</td>
</tr><tr>
<td colspan="1" rowspan="1">EoriNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">欧盟税号，非必填，格式为：前缀GB +9 位<br>纯数字＋后缀000 或者 前缀GB +12 位纯数字＋后缀000 </td>
</tr><tr>
<td colspan="1" rowspan="1">TaxCountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">2 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">税号所属国家</td>
</tr><tr>
<td colspan="1" rowspan="1">ImporterName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">255 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">进口商名称</td>
</tr><tr>
<td colspan="1" rowspan="1">ImporterAddress </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">255 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">进口商地址</td>
</tr><tr>
<td colspan="1" rowspan="1">TaxProve </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">255 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">税号证明</td>
</tr><tr>
<td colspan="1" rowspan="1">TaxRemark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">255 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">税号备注</td>
</tr><tr>
<td colspan="1" rowspan="1">WarehouseAddressCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">仓库代码</td>
</tr><tr>
<td colspan="1" rowspan="1">SizeUnits </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">长度单位，支持传输cm 、inch ，为空默认cm </td>
</tr><tr>
<td colspan="1" rowspan="1">Length </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">预估包裹单边长，单位与SizeUnits 联动，为<br>空默认1 </td>
</tr><tr>
<td colspan="1" rowspan="1">Width </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">预估包裹单边宽，单位与SizeUnits 联动，为<br>空默认1 </td>
</tr><tr>
<td colspan="1" rowspan="1">Height </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">预估包裹单边高，单位与SizeUnits 联动，为<br>空默认1 </td>
</tr><tr>
<td colspan="1" rowspan="1">PackageCount </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">运单包裹的件数，必须大于0 的整数</td>
</tr><tr>
<td colspan="1" rowspan="1">Weight </td>
<td colspan="1" rowspan="1">decimal( 18,3) </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">预估包裹总重量，单位kg ，最多3 位小数</td>
</tr><tr>
<td colspan="1" rowspan="1">Receiver </td>
<td colspan="1" rowspan="1">Receiver </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">收件人信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Sender </td>
<td colspan="1" rowspan="1">Sender </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人信息</td>
</tr><tr>
<td colspan="1" rowspan="1">ApplicationType </td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">申报类型，用于打印CN22 ，<br>1-Gift,2-Sameple,3-Documents,4-Others,<br>默认 4-Other </td>
</tr><tr>
<td colspan="1" rowspan="1">ReturnOption </td>
<td colspan="1" rowspan="1">bool </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">是否退回，包裹无人签收时是否退回，1 -退回，0 -不退回，默认0 </td>
</tr><tr>
<td colspan="1" rowspan="1">TariffPrepay </td>
<td colspan="1" rowspan="1">bool </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">关税预付服务费，1 -参加关税预付，0 -不参加<br>关税预付，默认0 （渠道需开通关税预付<br>服务）</td>
</tr><tr>
<td colspan="1" rowspan="1">InsuranceOption </td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">包裹投保类型，0 -不参保，1 -按件，2 -按比例，默认0 ，表示不参加运输保险，具体参考<br>包裹运输</td>
</tr><tr>
<td colspan="1" rowspan="1">Coverage </td>
<td colspan="1" rowspan="1">decimal( 18,2) </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">保险的最高额度，单位 RMB </td>
</tr><tr>
<td colspan="1" rowspan="1">SensitiveTypeID </td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">包裹中特殊货品类型，可调用货品类型查<br>询服务查询，可以不填写，表示普通货品</td>
</tr><tr>
<td colspan="1" rowspan="1">Parcels </td>
<td colspan="1" rowspan="1">Parcels[]</td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">申报信息</td>
</tr><tr>
<td colspan="1" rowspan="1">SourceCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">3 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">订单来源代码</td>
</tr><tr>
<td colspan="1" rowspan="1">ChildOrders </td>
<td colspan="1" rowspan="1">ChildOr ders[]</td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">箱子明细信息，FBA  订单必填</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderExtra </td>
<td colspan="1" rowspan="1">OrderEx trall </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">附加服务；<br>走欧盟税改IOSS 流程的订单，IossCode或OrderExtra 必须二者填一</td>
</tr><tr>
<td colspan="1" rowspan="1">IossCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">IOSS 备案识别码或IOSS 号；<br>走欧盟税改IOSS 流程的订单，IossCode或OrderExtra 必须二者填一</td>
</tr><tr>
<td colspan="1" rowspan="1">DangerousGoodsType </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">30 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">货物涉及危险品需申报危险品类型，例<br>如：810 ，参考【危险品代码表】 </td>
</tr><tr>
<td colspan="1" rowspan="1">Platform </td>
<td colspan="1" rowspan="1">Platform </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">平台信息</td>
</tr></table>

<!-- 25/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

Receiver类型

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">收件人所在国家，填写国际通用标准2 位简码，可通过国家查询服务查询</td>
</tr><tr>
<td colspan="1" rowspan="1">FirstName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">收件人姓</td>
</tr><tr>
<td colspan="1" rowspan="1">LastName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人名字</td>
</tr><tr>
<td colspan="1" rowspan="1">Company </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人公司名称</td>
</tr><tr>
<td colspan="1" rowspan="1">Street </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">收件人详细地址</td>
</tr><tr>
<td colspan="1" rowspan="1">StreetAddress1 </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人详细地址 1 </td>
</tr><tr>
<td colspan="1" rowspan="1">StreetAddress2 </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人详细地址2 </td>
</tr><tr>
<td colspan="1" rowspan="1">City </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">收件人所在城市</td>
</tr><tr>
<td colspan="1" rowspan="1">State </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人省／州</td>
</tr><tr>
<td colspan="1" rowspan="1">Zip </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人邮编</td>
</tr><tr>
<td colspan="1" rowspan="1">Phone </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人电话</td>
</tr><tr>
<td colspan="1" rowspan="1">HouseNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人街道地址门牌号</td>
</tr><tr>
<td colspan="1" rowspan="1">Email </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人电子邮箱</td>
</tr><tr>
<td colspan="1" rowspan="1">MobileNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人手机号</td>
</tr><tr>
<td colspan="1" rowspan="1">CertificateCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">收件人ID ，中东专线-约旦国家必填，10 位数字</td>
</tr></table>

<!-- 26/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

Sender类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">10 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人所在国家，填写国际通用标准2 位简码，<br>可<br>通过国家查询服务查询</td>
</tr><tr>
<td colspan="1" rowspan="1">FirstName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人姓</td>
</tr><tr>
<td colspan="1" rowspan="1">LastName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人名</td>
</tr><tr>
<td colspan="1" rowspan="1">Company </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">500 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人公司名称</td>
</tr><tr>
<td colspan="1" rowspan="1">Street </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人详细地址 FBA 必填</td>
</tr><tr>
<td colspan="1" rowspan="1">City </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人所在城市</td>
</tr><tr>
<td colspan="1" rowspan="1">State </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人省／州</td>
</tr><tr>
<td colspan="1" rowspan="1">Zip </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人邮编</td>
</tr><tr>
<td colspan="1" rowspan="1">Phone </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">20 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人电话</td>
</tr><tr>
<td colspan="1" rowspan="1">Email </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">150 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人邮箱</td>
</tr><tr>
<td colspan="1" rowspan="1">UsciCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">发件人营业执照上的统一社会信用代码</td>
</tr></table>

Parcels类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">EName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">包裹申报名称（英文）必填</td>
</tr><tr>
<td colspan="1" rowspan="1">CName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">500 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">包裹申报名称（中文）</td>
</tr><tr>
<td colspan="1" rowspan="1">HSCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">海关编码</td>
</tr><tr>
<td colspan="1" rowspan="1">Quantity </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">申报数量，必填</td>
</tr><tr>
<td colspan="1" rowspan="1">UnitPrice </td>
<td colspan="1" rowspan="1">decimal( 18,2) </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">申报价格（单价），必填</td>
</tr><tr>
<td colspan="1" rowspan="1">UnitWeight </td>
<td colspan="1" rowspan="1">decimal( 18,3) </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">申报重量（单重），单位kg ，</td>
</tr><tr>
<td colspan="1" rowspan="1">Remark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">500 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">订单备注，用于打印配货单</td>
</tr><tr>
<td colspan="1" rowspan="1">ProductUrl </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">产品销售链接地址</td>
</tr><tr>
<td colspan="1" rowspan="1">SKU </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">用于填写商品 SKU , FBA 订单必填</td>
</tr><tr>
<td colspan="1" rowspan="1">InvoiceRemark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">配货信息</td>
</tr><tr>
<td colspan="1" rowspan="1">CurrencyCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">申报币种，默认USD ，英国支持GBP /EUR ，欧盟国家<br>支持EUR。</td>
</tr><tr>
<td colspan="1" rowspan="1">Attachment </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">255 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">SKU 附件</td>
</tr><tr>
<td colspan="1" rowspan="1">InvoicePart </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">255 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">材质</td>
</tr><tr>
<td colspan="1" rowspan="1">InvoiceUsage </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">255 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">用途</td>
</tr></table>

OrderExtra类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">ExtraCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">20 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">额外服务代码：<br>GO 代表关税预付；<br>10 代表报关件；<br>V1 代表云途预缴IOSS 附加服务费（走欧盟税改IOSS </td>
</tr><tr>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">流程的订单，IossCode或OrderExtra 必须二者填一）；<br>VAS _IP 代表保价服务；<br>Ls0091 代表签名服务；<br>V4 代表云途预缴增值税号附加服务费，<br>TaxNumber 或OrderExtra 二者填一；</td>
</tr><tr>
<td colspan="1" rowspan="1">ExtraName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">20 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">额外服务名称</td>
</tr><tr>
<td colspan="1" rowspan="1">ExtraValue </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">30 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">额外服务值：<br>若额外服务代码为VAS _IP ，此项必传。<br>BJFA：固定金额<br>BJFR：运费＊费率<br>BJDR ：申报价值＊费率<br>BJFDR :（运费＋申报价值）＊费率</td>
</tr><tr>
<td colspan="1" rowspan="1">ExtraNote </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">额外服务备注</td>
</tr></table>

<!-- 27/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

ChildOrders 类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">BoxNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">箱子编号，FBA 订单必填</td>
</tr><tr>
<td colspan="1" rowspan="1">ReferenceID </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">FBA 订单＆销售产品为“FBAUSSEA-K和FBAUSSEA-<br>T ”时必填</td>
</tr><tr>
<td colspan="1" rowspan="1">Length </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">预估包裹单边长，单位cm ，默认1 , FBA 订单必填</td>
</tr><tr>
<td colspan="1" rowspan="1">Width </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">预估包裹单边宽，单位cm ，默认1 , FBA 订单必填</td>
</tr><tr>
<td colspan="1" rowspan="1">Height </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">预估包裹单边高，单位cm ，默认1 , FBA 订单必填</td>
</tr><tr>
<td colspan="1" rowspan="1">BoxWeight </td>
<td colspan="1" rowspan="1">decimal(1 8,3) </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">预估包裹总重量，单位kg ，最多3 位小数，FBA<br>订<br>单必填</td>
</tr><tr>
<td colspan="1" rowspan="1">ChildDetails </td>
<td colspan="1" rowspan="1">ChildDet ails[]</td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">单箱 SKU 信息，FBA  订单必填</td>
</tr></table>

ChildDetails 类型

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">SKU </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">用于填写商品 SKU , FBA 订单必填</td>
</tr><tr>
<td colspan="1" rowspan="1">Quantity </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">申报数量，FBA 订单必填</td>
</tr></table>

Platform类型

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">PlatformName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">平台名称</td>
</tr><tr>
<td colspan="1" rowspan="1">Province </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">州省</td>
</tr><tr>
<td colspan="1" rowspan="1">Address </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">地址</td>
</tr><tr>
<td colspan="1" rowspan="1">PostalCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">20 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">邮编</td>
</tr><tr>
<td colspan="1" rowspan="1">PhoneNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">联系电话</td>
</tr><tr>
<td colspan="1" rowspan="1">Email </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">邮箱</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">OrderResponse []</td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

<!-- 28/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<!-- 29/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

OrderResponse类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CustomerOrderNumber </td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">客户的订单号</td>
</tr><tr>
<td colspan="1" rowspan="1">Success </td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1">运单请求状态，1 -成功，0 -失败</td>
</tr><tr>
<td colspan="1" rowspan="1">TrackType </td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">1 -已产生跟踪号，2 -等待后续更新跟踪号，3 -不需要跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">Remark </td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">运单请求失败反馈失败原因</td>
</tr><tr>
<td colspan="1" rowspan="1">RequireSenderAddress </td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1">0 -不需要分配地址，1 -需要分配地址</td>
</tr><tr>
<td colspan="1" rowspan="1">AgentNumber </td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">代理单号</td>
</tr><tr>
<td colspan="1" rowspan="1">WayBillNumber </td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">YT 运单号</td>
</tr><tr>
<td colspan="1" rowspan="1">TrackingNumber </td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">ShipperBoxs </td>
<td colspan="1" rowspan="1">ShipperBoxs[]</td>
<td colspan="1" rowspan="1">箱子信息</td>
</tr></table>

ShipperBoxs **类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">BoxNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">箱子号码</td>
</tr><tr>
<td colspan="1" rowspan="1">ShipperHawbcode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">物流运单子单号</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u><u>&nbsp;</u>详细说明。

消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/WayBill/CreateOrde</u>r（支持批量申请，一次最多10条）请求数据

例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">[<br>{<br>"CustomerOrderNumber": "G2G20190402001",<br>"ShippingMethodCode": "PK0004",<br>"TrackingNumber": null,<br>"TransactionNumber": null,<br>"IossCode": "IOSS0690112210251452600",<br>"BrazilianCode": null, </td>
</tr><tr>
<td colspan="1" rowspan="1">"SizeUnits": "cm",<br>"Height":1,<br>"Length":1,<br>"Width":1,<br>"PackageCount":1,<br>"Weight":1,<br>"ApplicationType":4,<br>"ReturnOption":0,<br>"TariffPrepay":0,<br>"InsuranceOption": 0,<br>"SourceCode":"API",<br>"Receiver":{<br>"CountryCode": "US",<br>"FirstName":"xin",<br>"LastName":"ming",<br>"Company": "test gs",<br>"Street": "67700 Lockwood-Jolon Road",<br>"City": "Lockwood",<br>"State": "California",<br>"Zip":"93932",<br>"Phone": "5869098233",<br>"HouseNumber": "1",<br>"Email":"<EMAIL>"<br>},<br>"Sender":<br>{ "CountryCode":"US",<br>"FirstName":"test",<br>"LastName":"ming",<br>"Company": "test gs",<br>"Street": "207 TELLURIDE DR",<br>"City": "GEORGETOWN",<br>"State": "TX",<br>"Zip": "78626-7163",<br>"Phone": "5869098233",<br>"HouseNumber": "1"<br>},<br>"OrderExtra":[<br>{<br>"ExtraCode": "V1",<br>＂ExtraName"：“云途预缴”<br>}<br>],<br>"Parcels":[<br>{<br>"EName": "shangpin1",<br>＂CName":＂商品1"，<br>"HSCode": null,<br>"Quantity":1,<br>"SKU": "sku1001",<br>"UnitPrice": 10,<br>"UnitWeight":1,<br>＂Remark":＂商品＂，<br>"InvoiceRemark": "ceshi 1 pcs",<br>"CurrencyCode": null<br>}</td>
</tr><tr>
<td colspan="1" rowspan="1">],<br>"ChildOrders":[<br>{<br>"BoxNumber": "test01",<br>"Length":1,<br>"Width":1,<br>"Height":1,<br>"BoxWeight":1,<br>"ChildDetails":[<br>{<br>"Sku": "sku1001",<br>"Quantity":1<br>}<br>]<br>｝了<br>]<br>}<br>]</td>
</tr></table>

<!-- 30/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<!-- 30/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<!-- 31/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

#### 成功响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">{1<br>"Item":[<br>"CustomerOrderNumber": "G2G20190402001",<br>"Success":1,<br>"TrackType":"2",<br>"Remark":"",<br>"AgentNumber":null,<br>"WayBilINumber":"YT1909231204000001",<br>"Requi reSenderAddress":0,<br>"TrackingNumber":null,<br>"ShipperBoxs":[<br>"BoxNumber":"test01",<br>"ShipperHawbcode": "YT1909231204000001U001"<br>]<br>],<br>"Code":"0000",<br>＂Message":＂提交成功＂，<br>"RequestId":"857243d4ea764b65a408fb10e386c937",<br>"TimeStamp":"2019-04-02T05:47:00.3356733+00:00"</td>
</tr></table>

#### 失败响应数据包示例：

{

<table border="1" ><tr>
<td colspan="1" rowspan="1">"Item":[{</td>
<td colspan="1" rowspan="1">"CustomerOrderNumber":"G2G20190402002",<br>"Success":0,<br>"TrackType":null,<br>＂Remark ":＂目前的运输方式无法到达目的地国家／地区”，<br>"AgentNumber":null,<br>"WayBiIINumber":null,<br>"RequireSenderAddress":0,<br>"TrackingNumber":null,</td>
</tr><tr>
<td colspan="2" rowspan="1">"ShipperBoxs":null<br>}<br>],<br>"Code":"1001",<br>＂Message":＂提交失败”，<br>"RequestId":"8911518b629d4a68af1ba13704f1c0f6",<br>"TimeStamp":"2019-04-02T05:54:32_86693+00:00"<br>}</td>
</tr></table>

<!-- 32/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<!-- 33/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

### 2.8 查询运单

接口功能描述

客户端向 OMS请求运单查询。

事件流

基本流：

A.客户端向OMS提交查询运单请求；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据。

接口地址

<u>http://ADDR/api/WayBill/GetOrder</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">物流系统运单号，客户订单或跟踪号</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">WaybillOrder </td>
<td colspan="1" rowspan="1">添加成功的订单信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

Waybil10rder类型**：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">WayBillNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">物流系统运单号</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">客户订单号</td>
</tr><tr>
<td colspan="1" rowspan="1">ShippingMethodCod e </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发货的方式</td>
</tr><tr>
<td colspan="1" rowspan="1">TrackingNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">包裹跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">TransactionNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">平台交易号</td>
</tr><tr>
<td colspan="1" rowspan="1">Length </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">预估包裹单边长，单位cm </td>
</tr><tr>
<td colspan="1" rowspan="1">Width </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">预估包裹单边宽，单位cm </td>
</tr><tr>
<td colspan="1" rowspan="1">Height </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">预估包裹单边高，单位cm </td>
</tr><tr>
<td colspan="1" rowspan="1">Status </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">订单状态：0 -草稿，3 -已提交，4 -已收货，5 -已发货，6 -已删<br>除，7 -已退回，11 -已签收，10 -已理赔</td>
</tr><tr>
<td colspan="1" rowspan="1">PackageNumber </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">运单的包裹件数</td>
</tr><tr>
<td colspan="1" rowspan="1">Weight </td>
<td colspan="1" rowspan="1">decimal( 18,3) </td>
<td colspan="1" rowspan="1">预估包裹总重量，单位kg </td>
</tr><tr>
<td colspan="1" rowspan="1">Receiver </td>
<td colspan="1" rowspan="1">Receiver </td>
<td colspan="1" rowspan="1">收件人信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Sender </td>
<td colspan="1" rowspan="1">Sender </td>
<td colspan="1" rowspan="1">发件人信息</td>
</tr><tr>
<td colspan="1" rowspan="1">ApplicationType </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">申报类型</td>
</tr><tr>
<td colspan="1" rowspan="1">ReturnOption </td>
<td colspan="1" rowspan="1">bool </td>
<td colspan="1" rowspan="1">是否退回，包裹无人签收时是否退回，true -退回，false -不退回</td>
</tr><tr>
<td colspan="1" rowspan="1">InsuranceType </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">包裹投保类型，0 -不参保，1 -按件，2 -按比例</td>
</tr><tr>
<td colspan="1" rowspan="1">InsureAmount </td>
<td colspan="1" rowspan="1">decimal( 18,2) </td>
<td colspan="1" rowspan="1">保险的最高额度，单位 RMB </td>
</tr><tr>
<td colspan="1" rowspan="1">SensitiveTypeID </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">包裹中特殊货品类型</td>
</tr><tr>
<td colspan="1" rowspan="1">Parcels </td>
<td colspan="1" rowspan="1">Parcels[]</td>
<td colspan="1" rowspan="1">申报信息</td>
</tr><tr>
<td colspan="1" rowspan="1">ChargeWeight </td>
<td colspan="1" rowspan="1">decimal(1 8,3) </td>
<td colspan="1" rowspan="1">计费重量</td>
</tr></table>

<!-- 34/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

Receiver类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">TaxId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收件人企业税号</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收件人所在国家简码</td>
</tr><tr>
<td colspan="1" rowspan="1">FirstName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收件人姓</td>
</tr><tr>
<td colspan="1" rowspan="1">LastName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收件人名字</td>
</tr><tr>
<td colspan="1" rowspan="1">Company </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收件人公司名称</td>
</tr><tr>
<td colspan="1" rowspan="1">Street </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收件人详情地址</td>
</tr><tr>
<td colspan="1" rowspan="1">StreetAddress1 </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收件人详细地址 1 </td>
</tr><tr>
<td colspan="1" rowspan="1">StreetAddress2 </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收件人详细地址2 </td>
</tr><tr>
<td colspan="1" rowspan="1">City </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收件人所在城市</td>
</tr><tr>
<td colspan="1" rowspan="1">State </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收货人省／州</td>
</tr><tr>
<td colspan="1" rowspan="1">Zip </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收货人邮编</td>
</tr><tr>
<td colspan="1" rowspan="1">Phone </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收货人电话</td>
</tr><tr>
<td colspan="1" rowspan="1">HouseNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">收件人地址门牌号</td>
</tr></table>

Sender类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人所在国家简码</td>
</tr><tr>
<td colspan="1" rowspan="1">FirstName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人姓</td>
</tr><tr>
<td colspan="1" rowspan="1">LastName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人名字</td>
</tr><tr>
<td colspan="1" rowspan="1">Company </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人公司名称</td>
</tr><tr>
<td colspan="1" rowspan="1">Street </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人详情地址</td>
</tr><tr>
<td colspan="1" rowspan="1">City </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发件人所在城市</td>
</tr><tr>
<td colspan="1" rowspan="1">State </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发货人省／州</td>
</tr><tr>
<td colspan="1" rowspan="1">Zip </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发货人邮编</td>
</tr><tr>
<td colspan="1" rowspan="1">Phone </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">发货人电话</td>
</tr></table>

<!-- 35/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

Parcels类型

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Sku </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">包裹中货品 商品 SKU </td>
</tr><tr>
<td colspan="1" rowspan="1">EName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">包裹中货品 申报名称（英文）</td>
</tr><tr>
<td colspan="1" rowspan="1">CName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">包裹中货品 申报名称（中文）</td>
</tr><tr>
<td colspan="1" rowspan="1">HSCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">包裹中货品 申报编码</td>
</tr><tr>
<td colspan="1" rowspan="1">Quantity </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">包裹中货品 申报数量</td>
</tr><tr>
<td colspan="1" rowspan="1">UnitPrice </td>
<td colspan="1" rowspan="1">decimal( 18,2) </td>
<td colspan="1" rowspan="1">包裹中货品 申报价格</td>
</tr><tr>
<td colspan="1" rowspan="1">UnitWeight </td>
<td colspan="1" rowspan="1">decimal( 18,3) </td>
<td colspan="1" rowspan="1">包裹中货品 申报重量</td>
</tr><tr>
<td colspan="1" rowspan="1">Remark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">材质，用途等备注</td>
</tr><tr>
<td colspan="1" rowspan="1">ProductUrl </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">产品销售链接地址</td>
</tr><tr>
<td colspan="1" rowspan="1">InvoiceRemark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">配货信息</td>
</tr><tr>
<td colspan="1" rowspan="1">CurrencyCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">申报币种</td>
</tr><tr>
<td colspan="1" rowspan="1">ClassCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">品类编码</td>
</tr><tr>
<td colspan="1" rowspan="1">Brand </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">品牌</td>
</tr><tr>
<td colspan="1" rowspan="1">ModelType </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">型号规格</td>
</tr><tr>
<td colspan="1" rowspan="1">Unit </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">单位</td>
</tr></table>

<!-- 36/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

消息示例

消息示例

**请求数据包示例：**

GET请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/WayBill/GetOrder?OrderNumber=YT1908821203000021</u>

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{</td>
</tr><tr>
<td colspan="1" rowspan="1">"WayBillNumber": "YT1908821203000021",<br>"OrderNumber": "test-20190322450110",<br>"ShippingMethodCode": "PK0003",<br>"TrackingNumber":"",<br>"TransactionNumber": null,<br>"Length":1,<br>"Width":1,<br>"Height":1,<br>"PackageNumber": 1,<br>"Weight": 1.000,<br>"ApplicationType": 4,<br>"ReturnOption": 0,<br>"Status": 5,<br>"InsuranceType": null,<br>"InsureAmount": null,<br>"SensitiveTypeID": null,<br>"Receiver":{<br>"TaxId": null,<br>"CountryCode": "GB",<br>"FirstName": "test",<br>"LastName": null,<br>"Company": null,<br>"Street": "VIA GUGLIELMO OBERDAN 29",<br>"StreetAddress1": null,<br>"StreetAddress2": null,<br>"City": "RICCIONE",<br>"State": null,<br>"Zip":null,<br>"Phone": null,<br>"HouseNumber": null<br>},<br>"Sender": null,<br>"Parcels":[{<br>"Sku": "121",<br>"EName": "123",<br>"CName": "522654",<br>"HsCode": "111001",<br>"Quantity": 1,<br>"UnitPrice": 1.0,<br>"UnitWeight": 1.000,<br>"Remark": null,<br>"ProductUrl": "1",<br>"InvoiceRemark": "522654",<br>"CurrencyCode": null, </td>
</tr></table>

<!-- 37/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<!-- 38/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**失败响应数据包示例：**

参考<u>【失败响应】</u>详细说明。

### 2.9 修改订单预报重量

接口功能描述

客户端向 OMS发送需要修改的订单重量。

事件流

基本流：

A.客户端向OMS发送需要修改的订单重量；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据，每交最多返回1条。接口地址

<u>http://ADDR/api/WayBill/UpdateWeight</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">订单号</td>
</tr><tr>
<td colspan="1" rowspan="1">Weight </td>
<td colspan="1" rowspan="1">decimal </td>
<td colspan="1" rowspan="1">(18,3) </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">修改重量</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">OrderWeight </td>
<td colspan="1" rowspan="1">成功修改订单信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

<!-- 39/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

OrderWeight类型**：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">订单号</td>
</tr><tr>
<td colspan="1" rowspan="1">Status </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">处理结果 'success＇表示修改成功，'failure＇<br>表示修改失败</td>
</tr><tr>
<td colspan="1" rowspan="1">Remark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">错误信息</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

消息示例

消息示例

**请求数据包示例：**

POST 请求，数据类型请用 json类型，请求数据为：

<u>http://ADDR/api/WayBill/UpdateWeight</u>

{"OrderNumber":"G2Gapi20190401001","Weight":"2.88"}

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{<br>"OrderNumber": "G2Gapi20190401001",<br>"Status": "success",<br>＂Remark":＂处理成功＂<br>},<br>"Code": "0000",<br>＂Message":＂提交成功＂，<br>"RequestId": "5b77d337a9c046508b4a5eb65dcc64b7",<br>"TimeStamp": "2019-04-01T09:13:34.6481748+00:00"<br>}</td>
</tr></table>

**失败响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{<br>"OrderNumber": "test-2019032000010020",<br>"Status": "failuure",<br>＂Remark ":＂订单不是已提交状态”<br>},<br>"Code":'"0000",<br>＂Message":＂提交成功＂，</td>
</tr></table>

<!-- 40/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

参考<u>【失败响应】</u>详细说明。

### 2.10 订单删除

接口功能描述

客户端向OMS发送需要删除的订单单号。

事件流

基本流：

A.客户端向OMS发送需要删除的订单单号；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据，每交最多返回1条。

接口地址

<u>http://ADDR/api/WayBill/Delete</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderType </td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">单号类型：1 -云途单号，2 -客户订单号，3 -跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">单号</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">OrderDelete </td>
<td colspan="1" rowspan="1">返回删除订单信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

<!-- 41/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**OrderDelete 类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Status </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回的结果码Success（成功）/Failure（成功）</td>
</tr><tr>
<td colspan="1" rowspan="1">Type </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">请求的单号类型</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求的单号</td>
</tr><tr>
<td colspan="1" rowspan="1">Remark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">处理信息</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

消息示例

消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/WayBill/Delete</u>

{"OrderType":"1","OrderNumber":"YT1908621203000037"}

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{<br>"OrderNumber": "YT1908621203000037",<br>"Status": "5012",<br>"Type":1,<br>＂Remark":＂删除成功”<br>},<br>"Code": "0000",<br>＂Message":＂提交成功＂，<br>"RequestId": "98d91dd88d3844429263655efc183036",<br>"TimeStamp": "2019-03-27T10:50:02.2419507+00:00"<br>｝了</td>
</tr></table>

**失败响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{<br>"OrderNumber": "YT1908021201000009", </td>
</tr></table>

<!-- 42/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

参考<u>【失败响应】</u>详细说明。

### 2.11 订单拦截

接口功能描述

客户端向OMS发送需要拦截的订单单号。

事件流

基本流：

A.客户端向OMS发送需要拦截的订单单号；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据，每交最多返回1条。接口地址

<u>http://ADDR/api/WayBill/Intercept</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderType </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">单号类型：1 -云途单号，2 -客户订单号，3 -跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">单号</td>
</tr><tr>
<td colspan="1" rowspan="1">Remark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">拦截原因</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">OrderIntercept </td>
<td colspan="1" rowspan="1">返回拦截订单信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

<!-- 43/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

OrderIntercept **类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Rueslt </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回的结果码</td>
</tr><tr>
<td colspan="1" rowspan="1">Type </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">请求的单号类型</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求的单号</td>
</tr><tr>
<td colspan="1" rowspan="1">Remark </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">处理信息</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

消息示例

消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/WayBill/Intercept</u>

｛"OrderType":"1","OrderNumber":"YT1908411202000003","Remark":＂客户要求拦截＂｝

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{<br>"OrderNumber": "YT1908411202000003",<br>"Result": "5011",<br>"Type":1,<br>＂Remark":＂拦截成功！”<br>},<br>"Code": "0000",<br>＂Message":＂提交成功＂，<br>"RequestId": "8bc4574346b54277bf85fe3d283ac1b6",<br>"TimeStamp": "2019-03-26T10:46:29.6169713+00:00"<br>}</td>
</tr></table>

#### 失败响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{<br>"OrderNumber": "YT1908021201000032", </td>
</tr></table>

<!-- 44/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

参考<u>【失败响应】</u>详细说明。

### 2.12 标签打印

接口功能描述

客户端向OMS请求标签打印地址。

事件流

基本流：

A.客户端向OMS请求标签打印地址请求；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据，每次最多返回50条。接口地址

<u>http://ADDR/api/Label/Print</u>

协议定义

请求包字段说明**：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">度</td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumbers </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">物流系统运单号，客户订单或跟踪号</td>
</tr></table>

<!-- 45/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">OrderLabelPrint[]</td>
<td colspan="1" rowspan="1">订单标签打印信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

OrderLabelPrint类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Url </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">打印标签地址</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderInfos </td>
<td colspan="1" rowspan="1">OrderInfo[]</td>
<td colspan="1" rowspan="1">订单详细信息</td>
</tr></table>

OrderInfo 类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CustomerOrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">客户订单号</td>
</tr><tr>
<td colspan="1" rowspan="1">Error </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">错误信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1">错误代码 100 -正确，200 -不存在打印模板</td>
</tr></table>

# 消息示例

消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/Label/Print</u>

["YT1907911202000015"]

成功响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":<br>[{"Url":<br>"http://gf.yunexpress.com/f9b95fb0-1ba1-4e44-8198-3ae1606b1e<br>3a.pdf",<br>"OrderInfos":[{<br>"CustomerOrderNumber": "YT1907911202000015",<br>＂Error":＂打印成功”，<br>"Code":100<br>}]<br>}],</td>
</tr><tr>
<td colspan="1" rowspan="1">"Code":"0000",<br>＂Message":＂提交成功＂，<br>"RequestId": "396b71e9be5d43dc81a31bdc9426df04",<br>"TimeStamp": "2019-04-01T09:19:44.882611+00:00"<br>}</td>
</tr></table>

<!-- 46/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**失败响应数据包示例：**

参考<u>【失败响应】</u>详细说明。

## 2.13 查询物流运费明细

接口功能描述

客户端向OMS请求地址验证。

事件流

基本流：

A.客户端向OMS 请求地址验证；

B. OMS 验证客户端信息合法性：

·检查请求数据的格式

C. OMS验证完信息后，以RETURN的形式返回响应数据。

接口地址

<u>http://ADDR/api/Freight/GetShippingFeeDetail</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">WayBillNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">运单号</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">FeeDetail </td>
<td colspan="1" rowspan="1">客户信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

<!-- 47/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**FeeDetail类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">ShippingMethodName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">销售产品</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">国家二字码</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">国家中文名</td>
</tr><tr>
<td colspan="1" rowspan="1">CustomerOrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">客户订单号</td>
</tr><tr>
<td colspan="1" rowspan="1">WayBillNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">运单号</td>
</tr><tr>
<td colspan="1" rowspan="1">TrackingNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">GrossWeight </td>
<td colspan="1" rowspan="1">decimal </td>
<td colspan="1" rowspan="1">称重重量</td>
</tr><tr>
<td colspan="1" rowspan="1">OccurrenceTime </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">费用发生时间</td>
</tr><tr>
<td colspan="1" rowspan="1">Freight </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">运费</td>
</tr><tr>
<td colspan="1" rowspan="1">FuelSurcharge </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">燃油费</td>
</tr><tr>
<td colspan="1" rowspan="1">RegistrationFee </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">挂号费</td>
</tr><tr>
<td colspan="1" rowspan="1">ProcessingFee </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">处理费</td>
</tr><tr>
<td colspan="1" rowspan="1">OtherFee </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">其它杂费</td>
</tr><tr>
<td colspan="1" rowspan="1">TotalFee </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">总费用</td>
</tr><tr>
<td colspan="1" rowspan="1">ChargeWeight </td>
<td colspan="1" rowspan="1">decimal </td>
<td colspan="1" rowspan="1">计费重</td>
</tr><tr>
<td colspan="1" rowspan="1">StandardMoney </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">本位币总费用</td>
</tr></table>

消息示例

消息示例

## 请求数据包示例：

GET请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/Freight/GetShippingFeeDetail?wayBillNNumber=YT190842120</u>

## <u>1000005</u>

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">"Item":{<br>＂ShippingMethodName":＂英国专线标准＂，<br>"CountryCode": "AF",<br>＂ChineseName":＂阿富汗＂，<br>"CustomerOrderNumber":<br>"201903251700", "WayBillNumber":<br>"YT1908421201000005",<br>"TrackingNumber":[""],</td>
</tr></table>

<!-- 48/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**未找到数据示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Code": "1006",<br>＂Message ":＂未找到数据查无数据＂，<br>"Item": null,<br>"RequestId": "f30642a81ad541309a97170e7c118cf3",<br>"TimeStamp": "2019-03-26T10:34:01.7263145+00:00"<br>了</td>
</tr></table>

参考<u>【失败响应】</u>详细说明。

## 2.14 用户注册

接口功能描述

客户端向 OMS请求用户注册。

事件流

基本流：

A.客户端向OMS 请求用户注册；

B.OMS 验证客户端信息合法性：

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据。

<!-- 49/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

接口地址

<u>http://ADDR/api/Common/Register</u>

## 协议定义

请求包字段说明：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">UserName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">用户名</td>
</tr><tr>
<td colspan="1" rowspan="1">PassWord </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">密码</td>
</tr><tr>
<td colspan="1" rowspan="1">Contact </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">联系人</td>
</tr><tr>
<td colspan="1" rowspan="1">Mobile </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">联系人电话</td>
</tr><tr>
<td colspan="1" rowspan="1">Telephone </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">联系人电话</td>
</tr><tr>
<td colspan="1" rowspan="1">Name </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">客户名称／公司名称</td>
</tr><tr>
<td colspan="1" rowspan="1">Email </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">邮箱</td>
</tr><tr>
<td colspan="1" rowspan="1">Address </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">详细地址</td>
</tr><tr>
<td colspan="1" rowspan="1">PlatForm </td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1">1 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">平台ID （通途平台--2 ）</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">Customer </td>
<td colspan="1" rowspan="1">客户信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

Customer类型：

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Customer </td>
<td colspan="1" rowspan="1">Customel </td>
<td colspan="1" rowspan="1">注册用户返回数据集</td>
</tr></table>

**CustomerModel 类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">CustomerCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">用户代码</td>
</tr><tr>
<td colspan="1" rowspan="1">UserName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">用户登录账号</td>
</tr><tr>
<td colspan="1" rowspan="1">SecretKey </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">API  对接密钥</td>
</tr></table>

# 消息示例

# 消息示例

**请求数据包示例：**

GET请求，数据类型请用json类型，请求数据为：

<!-- 50/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<u>http://ADDR/api/Common/Register</u>

**请求数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"UserName": "test126",<br>"PassWord": "123456",<br>＂Contact":＂老张＂，<br>"Telephone": "123456",<br>"Mobile": "***********",<br>＂Name":＂老张牛＂，<br>"Email": "<EMAIL>",<br>＂Address ":＂杨美地铁站东海王＂，<br>"Platform": 2<br>}</td>
</tr></table>

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{<br>"CustomerCode": "C12345",<br>"AccountID": "tongtu011",<br>"ApiSecret": "SIpDkN10x9b="<br>},<br>"Code": "0000",<br>＂Message":＂提交成功＂，<br>"RequestId": "a0ae1d861f9541d8a66312945fd7dc6d",<br>"Timestamp": "2019-03-07T09:54:09.7221676+00:00"<br>}</td>
</tr></table>

参考<u>【失败响应】</u><u>&nbsp;</u>详细说明。

## 2.15 查询物流轨迹信息

接口功能描述

客户端向OMS请求物流轨迹信息。

事件流

基本流：

A.客户端向OMS请求物流轨迹信息；

B.OMS验证客户端信息合法性：

·检查请求数据的格式

<!-- 51/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

## C.OMS验证完信息后，以RETURN的形式返回响应数据。

## 接口地址

根据轨迹订阅查询轨迹：<u>http://ADDR/api/Tracking/GetTrackInfo</u>

查询全程轨迹：<u>http://ADDR/api/Tracking/GetTrackAllInfo</u> 协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">物流系统运单号，客户订单或跟踪号</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">item </td>
<td colspan="1" rowspan="1">请求成功的物流运单跟踪信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

**Item类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">目的地国家简码</td>
</tr><tr>
<td colspan="1" rowspan="1">WaybillNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">运单号</td>
</tr><tr>
<td colspan="1" rowspan="1">TrackingNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">ProviderName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">末端服务商名称</td>
</tr><tr>
<td colspan="1" rowspan="1">ProviderTelephone </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">末端服务商联系方式</td>
</tr><tr>
<td colspan="1" rowspan="1">ProviderSite </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">末端服务商官网</td>
</tr><tr>
<td colspan="1" rowspan="1">POD </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">单个POD 链接（妥投证明）信息（URL 地址）</td>
</tr><tr>
<td colspan="1" rowspan="1">PODs </td>
<td colspan="1" rowspan="1">string[]</td>
<td colspan="1" rowspan="1">多个POD 链接（妥投证明）信息（URL 地址）</td>
</tr><tr>
<td colspan="1" rowspan="1">CreatedBy </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">创建人</td>
</tr><tr>
<td colspan="1" rowspan="1">PackageState </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">包裹状态<br>0 -未知，1 -已提交 2 -运输中 3 -已签收，4 -已收货，5 -订单取消，6 -投递失败，7 -已退回</td>
</tr><tr>
<td colspan="1" rowspan="1">IntervalDays </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">包裹签收天数</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderTrackingDetails </td>
<td colspan="1" rowspan="1">OrderTrackingDetai 1s[]</td>
<td colspan="1" rowspan="1">订单跟踪详情</td>
</tr></table>

**OrderTrackingDetails 类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">ProcessDate </td>
<td colspan="1" rowspan="1">dateTime </td>
<td colspan="1" rowspan="1">包裹请求日期</td>
</tr><tr>
<td colspan="1" rowspan="1">ProcessContent </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">包裹请求内容</td>
</tr><tr>
<td colspan="1" rowspan="1">ProcessLocation </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">包裹请求地址</td>
</tr><tr>
<td colspan="1" rowspan="1">TrackNodeCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">轨迹节点代码</td>
</tr><tr>
<td colspan="1" rowspan="1">TrackCodeDescription </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">轨迹节点英文描述</td>
</tr><tr>
<td colspan="1" rowspan="1">ProcessCountry </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">轨迹发生地所在国家</td>
</tr><tr>
<td colspan="1" rowspan="1">ProcessProvince </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">轨迹发生地所在省州</td>
</tr><tr>
<td colspan="1" rowspan="1">ProcessCity </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">轨迹发生地所在城市</td>
</tr><tr>
<td colspan="1" rowspan="1">AbnormalReasons </td>
<td colspan="1" rowspan="1">AbnormalReasons[]</td>
<td colspan="1" rowspan="1">轨迹异常原因</td>
</tr></table>

<!-- 52/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**AbnormalReasons 类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">AbnormalReasonCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">轨迹异常原因代码</td>
</tr><tr>
<td colspan="1" rowspan="1">AbnormalReason </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">轨迹异常原因描述</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说

明。

# 消息示例

**请求数据包示例：**

GET请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/Tracking/GetTrackInfo?OrderNumber=YT1908321215012659</u>

<u>http://ADDR/api/Tracking/GetTrackAllInfo?OrderNumber=YT1908321215012659</u>

成功响应数据包示例**：**

<!-- 53/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<table border="1" ><tr>
<td colspan="1" rowspan="1">"Item":{<br>"CarrierName": "Mail Allience",<br>"CreatedBy":null,<br>"TrackingNumber":"",<br>"POD":null,<br>"PODs":[],<br>"ProviderSite":null,<br>"ProviderTelephone":null,<br>"PackageState":1,<br>"IntervalDays":-1.0,<br>"TrackingStatus":10,<br>"OriginCountryCode":"CN",<br>"ProviderName":null,<br>"WayBillNumber": "YT2207711010000002",<br>"OrderTrackingDetails":[<br>"TrackingStatus":10,<br>"TrackCodeDescription":null,<br>"AbnormalReasons":[<br>"AbnormalReasonCode":"NL000007"<br>"AbnormalReason":"Recipient's address abnormal"<br>}],<br>"ProcessLocation":null,<br>"ProcessProvince":"",<br>"ProcessContent": "Shipment information received",<br>"ProcessDate": "2022-03-18T16:48:44",</td>
</tr></table>

<table border="1" ><tr>
<td colspan="1" rowspan="1">"ProcessCountry":"",<br>"TrackNodeCode":null,<br>"ProcessCity":""<br>],<br>"CountryCode":"DE"<br>},<br>＂Message":＂提交成功！"，<br>"RequestId": "d90023a3f5934647875385d1c6d8cd8a",<br>"Code":"0000",<br>"TimeStamp": "2022-11-04T06:37:47.1890374+00:00"</td>
</tr></table>

## 2.16查询末端派送商

接口功能描述

客户端向 OMS请求物流轨迹信息。

<!-- 54/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

## 事件流

## 基本流：

A.客户端向 OMS 提交查询末端派送商请求；

B.OMS 验证客户端信息合法性：

C.检查请求数据的格式

·OMS 验证完信息后，以RETURN的形式返回响应数据接口地址

<u>http://ADDR/api/Waybill/GetCarrier</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderNumbers </td>
<td colspan="1" rowspan="1">String[]</td>
<td colspan="1" rowspan="1">2000 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">查询号码，可输入运单号、订单号、跟踪号</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">Carrier ifn[]</td>
<td colspan="1" rowspan="1">请求成功的单号对应末端派送商信息</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

**Carrier ifn 类型：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求查询单号</td>
</tr><tr>
<td colspan="1" rowspan="1">OrderCodeType </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">查询单号类型，客户单号：C ，运单号：Y ，跟踪号：T </td>
</tr><tr>
<td colspan="1" rowspan="1">CarrierCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">末端派送商代码</td>
</tr><tr>
<td colspan="1" rowspan="1">CarrierCName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">末端派送商中文名</td>
</tr><tr>
<td colspan="1" rowspan="1">CarrierEName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">末端派送商英文名</td>
</tr><tr>
<td colspan="1" rowspan="1">CarrierPhone </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">联系电话</td>
</tr><tr>
<td colspan="1" rowspan="1">CarrierWebsite </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">官网</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

消息示例

消息示例

**请求数据包示例：**

<!-- 55/82 -->

<!-- 云途物流API接口开发规范（OMMS版） -->

<!-- 云途物流 EXPRESS -->

POST请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/Waybill/GetCarrier</u>

### 成功响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">"Item":[<br>{<br>"OrderCode": "YT2114422114000001",<br>"OrderCodeType": "Y",<br>"CarrierCode": "ODERBY03",<br>＂CarrierCName":＂拜拜33333"，<br>"CarrierEName": "ODERBY3",<br>"CarrierPhone":"",<br>"CarrierWebsite":1"<br>],<br>"Code": "0000",<br>＂Message":＂提交成功＂，<br>"RequestId": "bc93453a12f04ac8b39f2246efa17dde",<br>"TimeStamp": "2021-07-12T09:07:28.4323172+00:00"<br>}</td>
</tr></table>

失败响应数据包示例：

参考<u>【失败响应】</u>详细说明。

可通过API抓取全程轨迹的渠道：

<table border="1" ><tr>
<td colspan="1" rowspan="1">渠道中文名</td>
<td colspan="1" rowspan="1">渠道 Code </td>
<td colspan="1" rowspan="1">备注</td>
</tr><tr>
<td colspan="1" rowspan="1">英国专线挂号</td>
<td colspan="1" rowspan="1">GBZXR </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">英国专线平邮</td>
<td colspan="1" rowspan="1">GBZXA </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">中欧专线DDP 挂号</td>
<td colspan="1" rowspan="1">EUDDP </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">中欧专线 DDP 平邮</td>
<td colspan="1" rowspan="1">EUDDPG </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">中欧双清专线</td>
<td colspan="1" rowspan="1">EUZXDDP </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">中欧独轮车专线</td>
<td colspan="1" rowspan="1">EUZXSW </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">意大利专线挂号</td>
<td colspan="1" rowspan="1">ITZX </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">意大利专线平邮</td>
<td colspan="1" rowspan="1">ITZXA </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">中美独轮车专线</td>
<td colspan="1" rowspan="1">USZXSW </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">中美专线</td>
<td colspan="1" rowspan="1">USZXR </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">中美免泡专线</td>
<td colspan="1" rowspan="1">USZXMP </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">中美独轮车专线 VIP </td>
<td colspan="1" rowspan="1">USZXVIP </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">德国邮政挂号（特惠11 国）</td>
<td colspan="1" rowspan="1">SGRDGM </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">德国邮政平邮（特惠11 国）</td>
<td colspan="1" rowspan="1">SGADGM </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">德国专线 VIP 挂号</td>
<td colspan="1" rowspan="1">DEZXR </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">德国专线 VIP 平邮</td>
<td colspan="1" rowspan="1">DEZXA </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">华南快速小包平邮</td>
<td colspan="1" rowspan="1">CNDWA </td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">国际小包优＋</td>
<td colspan="1" rowspan="1">CNPOST-FYB </td>
<td colspan="1" rowspan="1"></td>
</tr></table>

<!-- 56/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

### 2.17 IOSS号备案

接口功能描述

**客户向云途请求IOSS号备案申请，返回云途备案识别码。**

事件流

基本流：

A.客户端向OMS请求IOSS备案；

B.OMS验证客户端信息合法性

·检查请求数据的格式

C. OMS验证完信息后，以RETURN的形式返回响应数据

接口地址

<u>http://ADDR/api/WayBill/RegisterIoss</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">IossType </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">1 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">“0 ”个人“1 ”平台</td>
</tr><tr>
<td colspan="1" rowspan="1">PlatformName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">平台名称，类型为“1 ”时需提供</td>
</tr><tr>
<td colspan="1" rowspan="1">IossNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">12 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">2 位字母加10 位数字，reg:^[a-zA-Z]{2}[0-9]{10}&#36;</td>
</tr><tr>
<td colspan="1" rowspan="1">Company </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">IOSS 号注册公司名称</td>
</tr><tr>
<td colspan="1" rowspan="1">Country </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">2 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">2 位国家简码</td>
</tr><tr>
<td colspan="1" rowspan="1">Street </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">IOSS 号街道地址</td>
</tr><tr>
<td colspan="1" rowspan="1">City </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">IOSS 号所在城市</td>
</tr><tr>
<td colspan="1" rowspan="1">Province </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">IOSS 号所在省／州</td>
</tr><tr>
<td colspan="1" rowspan="1">PostalCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">IOSS 号邮编</td>
</tr><tr>
<td colspan="1" rowspan="1">MobilePhone </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">200 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">IOSS 号手机号</td>
</tr><tr>
<td colspan="1" rowspan="1">Email </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">100 </td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">IOSS 号电子邮箱</td>
</tr><tr>
<td colspan="1" rowspan="1">FileUrl </td>
<td colspan="1" rowspan="1">string[]</td>
<td colspan="1" rowspan="1">item 最小数量：0 ，最大数量：5 ；单个字串长<br>度：0-255</td>
<td colspan="1" rowspan="1">非必填</td>
<td colspan="1" rowspan="1">IOSS 注册文件，IOSS 号审核不通过、发起修改时必填</td>
</tr></table>

<!-- 57/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">IossCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">备案成功返回的IOSS 号对应的备案识别码</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr><tr>
<td colspan="1" rowspan="1">IossStatus </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">IOSS 状态 1 通过 3 待审核</td>
</tr><tr>
<td colspan="1" rowspan="1">IossNote </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">IOSS 备注</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u><u>&nbsp;</u>详细说明。

消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/WayBill/RegisterIoss</u>

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"IossType":"1",<br>"PlatformName":"Amazon",<br>"IossNumber":"xy1234567890",<br>"Company":"test",<br>"Country":"CN",<br>"Street":"long dong road 3000.",<br>"City":"shen zhen",<br>"Province":"guang dong",<br>"PostalCode":"20000",<br>"MobilePhone":"test123",<br>"Email":"<EMAIL>",<br>"FileUr1":[<br>"https://www.baidu.com/38521.png",<br>"https://www.baidu.com/48596.JPG",<br>"https://www.baidu.com/18693.JPEG",<br>"https://www.baidu.com/13508.BMP",<br>"https://www.baidu.com/34658.PDF"<br>]<br>}</td>
</tr></table>

成功响应数据包示例：

<!-- 58/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"IossCode":"IOSS231041533101462574121",<br>"IossStatus": "3",<br>＂IossNote":＂新增IOSS 号备案，请等待审核”，<br>"Code":"0000",<br>＂Message":＂提交成功！”，<br>"RequestId": "a36f4d83937d4ac6a6001e0c4409df05",<br>"TimeStamp": "2023-04-14T07:33:10.1139486+00:00"</td>
</tr></table>

### 2.18 按单号订阅轨迹

接口功能描述

**客户系统发送按单号取消订阅请求，返回操作结果。**

事件流

基本流：

A.客户端向OMS请求按单号订阅轨迹；

B.OMS验证客户端信息合法性

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据

接口地址

<u>http://ADDR/api/tracking/CreatedOrderSubscribe</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">是否必填</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">DisplayMode </td>
<td colspan="1" rowspan="1">轨迹内容：<br>0 ：全程轨迹；<br>1 ：头程轨迹（国内轨迹）；<br>2 ：末端轨迹（国<br>外派送轨迹）；<br>3 ：隐藏轨迹（不显示任何轨迹）；4 ：电子预报信<br>息＋末端轨迹<br>（国外派送轨迹）</td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">轨迹内容：<br>0 ：全程轨迹；<br>1 ：头程轨迹（国内轨迹）；<br>2 ：末端轨迹（国外派送轨迹）；<br>3 ：隐藏轨迹（不显示任何轨<br>迹）；<br>4 ：电子预报信息</td>
</tr><tr>
<td colspan="1" rowspan="1">QueryMode </td>
<td colspan="1" rowspan="1">查询单号：<br>1 ：运单号；<br>2 ：订单号；<br>4 ：跟踪号；</td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">查询单号：<br>1 ：运单号；<br>2 ：订单号；<br>4 ：跟踪号；</td>
</tr><tr>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">3 ：运单号、订单号；<br>5 ：运单号、跟踪号；<br>6 ：订单号、跟踪号；<br>7 ：运单号、订单号、跟踪号；</td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">3 ：运单号、订单号；<br>5 ：运单号、跟踪号；<br>6 ：订单号、跟踪号；<br>7 ：运单号、订单号、跟踪号；</td>
</tr><tr>
<td colspan="1" rowspan="1">shipper_hawbcode</td>
<td colspan="1" rowspan="1">运单号</td>
<td colspan="1" rowspan="1">String[]</td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">运单号</td>
</tr></table>

<!-- 59/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">返回参数</td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">状态：true-成功；false-失败</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">代码</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码（10 ：请求参数错误 20 ：服务执行超时1001：提交失败！0000：提交成功！1011：部<br>分成功！其余都是系统自定义异常）</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">结果描述</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">一般为返回的错误消息</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">请求id </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回数据内容</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">时间戳</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

**Item 类型说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Success </td>
<td colspan="1" rowspan="1">boolean </td>
<td colspan="1" rowspan="1">状态：true-成功；false-失败</td>
</tr><tr>
<td colspan="1" rowspan="1">ErrorCode </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">0 成功1000 参数错误</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回的错误消息</td>
</tr><tr>
<td colspan="1" rowspan="1">Data </td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">返回数据内容</td>
</tr></table>

**Data 类型说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">NoCount </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">订阅失败的数量</td>
</tr><tr>
<td colspan="1" rowspan="1">OkCount </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">订阅成功数量</td>
</tr><tr>
<td colspan="1" rowspan="1">FailNumbers </td>
<td colspan="1" rowspan="1">string []</td>
<td colspan="1" rowspan="1">订阅失败的业务ID </td>
</tr></table>

## 请求失败响应包字段说明：

参考<u>【失败响应】</u>详细说明

**消息示例**

**请求数据包示例：**

<!-- 60/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/tracking/CreatedOrderSubscribe</u>

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"DisplayMode":5,<br>"QueryMode":4,<br>"shipper_hawbcode":[<br>"YT1907721208007572",<br>"YT1907821208001377"<br>]<br>}</td>
</tr></table>

## 成功响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{ "Success":tr<br>ue, "ErrorCode":0,<br>"Message":"",<br>"Data":{<br>"OkCount":2,<br>"NoCount":0,<br>"FailNumbers":[]<br>}<br>},<br>"Code":"0000",<br>＂Message":＂订阅成功＂，<br>"RequestId" : "0HM9GLB91V907:00000002",<br>"TimeStamp":"2021-06-16T09:48:09.151795+00:00"<br>}</td>
</tr></table>

<!-- 61/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

## 2.19 按单号取消轨迹订阅

接口功能描述

客户向云途请求按单号取消轨迹订阅，返回操作结果。

事件流

基本流：

A.客户端向OMS发送按单号取消轨迹订阅请求；

B.OMS验证客户端信息合法性

检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据

接口地址

<u>http://ADDR/api/tracking/CancelOrderSubscribe</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">是否必填</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">DisplayMode </td>
<td colspan="1" rowspan="1">轨迹内容：<br>0 ：全程轨迹；<br>1 ：头程轨迹（国<br>内轨迹）；<br>2 ：末端轨迹（国<br>外派送轨迹）；<br>3 ：隐藏轨迹（不显示任何轨迹）；<br>4 ：电子预报信<br>息＋末端轨迹（国外派送轨迹）</td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">轨迹内容：<br>0 ：全程轨迹；<br>1 ：头程轨迹（国内轨迹）；<br>2 ：末端轨迹（国外派送轨迹）；<br>3 ：隐藏轨迹（不显示任何轨迹）；4 ：电子预报信息</td>
</tr><tr>
<td colspan="1" rowspan="1">QueryMode </td>
<td colspan="1" rowspan="1">查询单号：<br>1 ：运单号；<br>2 ：订单号；<br>4 ：跟踪号；<br>3 ：运单号、订单号；<br>5 ：运单号、跟踪</td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">查询单号：<br>1 ：运单号；<br>2 ：订单号；<br>4 ：跟踪号；<br>3 ：运单号、订单号；<br>5 ：运单号、跟踪号；<br>6 ：订单号、跟踪号；</td>
</tr><tr>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">号；<br>6 ：订单号、跟踪号；<br>7 ：运单号、订单号、跟踪号；</td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">7 ：运单号、订单号、跟踪号；</td>
</tr><tr>
<td colspan="1" rowspan="1">shipper_hawbcode</td>
<td colspan="1" rowspan="1">运单号</td>
<td colspan="1" rowspan="1">String[]</td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">运单号</td>
</tr></table>

<!-- 62/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">返回参数</td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">返回参数</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">代码</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码（10 ：请求参数错误20 ：服务执行超时1001：提交失败！0000：提交成功！1011：部分成功！其余都是系统自定义异常）</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">结果描述</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">一般为返回的错误消息</td>
</tr><tr>
<td colspan="1" rowspan="1">Requestld </td>
<td colspan="1" rowspan="1">请求id </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回数据内容</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">时间戳</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

Item 类型说明：

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Success </td>
<td colspan="1" rowspan="1">boolean </td>
<td colspan="1" rowspan="1">状态：true-成功；false-失败</td>
</tr><tr>
<td colspan="1" rowspan="1">ErrorCode </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">0 成功1000 参数错误</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回的错误消息</td>
</tr><tr>
<td colspan="1" rowspan="1">Data </td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">返回数据内容</td>
</tr></table>

**Data 类型说明**：

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">NoCount </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">失败取消的数量</td>
</tr><tr>
<td colspan="1" rowspan="1">OkCount </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">取消成功数量</td>
</tr><tr>
<td colspan="1" rowspan="1">FailNumbers </td>
<td colspan="1" rowspan="1">string[]</td>
<td colspan="1" rowspan="1">取消订阅失败的业务ID </td>
</tr></table>

<!-- 63/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

# 消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/tracking/CancelOrderSubscribe</u>

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"DisplayMode":5,<br>"QueryMode":4,<br>"shipper_hawbcode":[<br>"YT1907721208007572",<br>"YT1907821208001377"<br>]<br>]</td>
</tr></table>

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{ "Success":tr<br>ue, "ErrorCode":0,<br>"Message":"",<br>"Data":{<br>"OkCount":2,<br>"NoCount":0,<br>"FailNumbers":[]<br>}<br>},<br>"Code":"0000",<br>＂Message":＂取消订阅成功＂，<br>"RequestId" : "0HM9GLB91V907:00000002",<br>"TimeStamp": "2021-06-16T09:48:09.151795+00:00"<br>}</td>
</tr></table>

<!-- 64/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

## 2.20 获取按订单号订阅轨迹的数据

接口功能描述

客户向云途发送获取按订单号订阅轨迹的数据请求，返回按订单号订阅轨迹列表。事件流

基本流：

A.客户端向OMS请求IOSS备案；

B.OMS验证客户端信息合法性

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据

接口地址

<u>http://ADDR/api/tracking/GetOrderSubscribe</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">是否必填</td>
</tr><tr>
<td colspan="1" rowspan="1">shipper_hawbcode</td>
<td colspan="1" rowspan="1">运单号集合</td>
<td colspan="1" rowspan="1">string[]</td>
<td colspan="1" rowspan="1">否</td>
</tr><tr>
<td colspan="1" rowspan="1">currentpage </td>
<td colspan="1" rowspan="1">当前页</td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">是</td>
</tr><tr>
<td colspan="1" rowspan="1">pagesize </td>
<td colspan="1" rowspan="1">页数</td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">是</td>
</tr><tr>
<td colspan="1" rowspan="1">StartDate </td>
<td colspan="1" rowspan="1">订阅时间（起始）</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">是</td>
</tr><tr>
<td colspan="1" rowspan="1">EndDate </td>
<td colspan="1" rowspan="1">订阅时间（结束）</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">是</td>
</tr></table>

**请求成功响应包字段说明**：

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">返回参数</td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">状态：true-成功；false-失败</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">代码</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码（10 ：请求参数错误20 ：服务执行超时1001 ：提交失败！<br>0000 ：提交成功！1011 ：部分成功！其余都是系统自定义异常）</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">结果描述</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">一般为返回的错误消息</td>
</tr><tr>
<td colspan="1" rowspan="1">Requestld </td>
<td colspan="1" rowspan="1">请求id </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回数据内容</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">时间戳</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

Item类型说明：

<!-- 65/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Success </td>
<td colspan="1" rowspan="1">bool </td>
<td colspan="1" rowspan="1">状态：true-成功；false-失败</td>
</tr><tr>
<td colspan="1" rowspan="1">Data </td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">单号订阅信息</td>
</tr></table>

Data 类型说明：

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CountNum </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">查询总数</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">object[]</td>
<td colspan="1" rowspan="1">订阅信息</td>
</tr></table>

**Data对象Item类型说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Id </td>
<td colspan="1" rowspan="1">long </td>
<td colspan="1" rowspan="1">Id </td>
</tr><tr>
<td colspan="1" rowspan="1">CustomerOrderId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">客户订单号</td>
</tr><tr>
<td colspan="1" rowspan="1">WaybillNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">运单号</td>
</tr><tr>
<td colspan="1" rowspan="1">TrackingNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">SubscribeType </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">订阅类型（轨迹内容）：<br>0 ：全程轨迹；<br>1 ：头程轨迹（国内轨迹）；<br>2 ：末端轨迹（国外派送轨迹）；<br>3 ：隐藏轨迹（不显示任何轨迹）；<br>4 ：电子预报信息＋末端轨迹（国外派送轨迹）</td>
</tr><tr>
<td colspan="1" rowspan="1">QueryNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">查询单号：<br>1 ：运单号；<br>2 ：订单号；<br>4 ：跟踪号；<br>3 ：运单号、订单号；<br>5 ：运单号、跟踪号；<br>6 ：订单号、跟踪号；<br>7 ：运单号、订单号、跟踪号；</td>
</tr><tr>
<td colspan="1" rowspan="1">SubscribeTime </td>
<td colspan="1" rowspan="1">DateTime </td>
<td colspan="1" rowspan="1">订阅时间</td>
</tr><tr>
<td colspan="1" rowspan="1">Subscriber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">订阅人</td>
</tr></table>

<!-- 66/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/WayBill/RegisterIoss</u>

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"shipper_hawbcode":[<br>],<br>"currentpage":1,<br>"pagesize":20,<br>"StartDate":"2020-03-02 10:23:33",<br>"EndDate":"2022-03-02 10:23:33"<br>}</td>
</tr></table>

### 成功响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{<br>"Success":true,<br>"data":{<br>"CountNum":11,<br>"Item":[<br>{<br>"Id":1696209,<br>"CustomerOrderId" : "C06901213090908131933698099",<br>"Waybil1Number": "YT2130922114000001",<br>"TrackingNumber":nul1,<br>"SubscribeType":"0",<br>"QueryNumber":"4",<br>"SubscribeTime": "2022-01-15T17:01:54",<br>"Subscriber":"2190"<br>"Id":1694059,<br>"CustomerOrderId":nul1,<br>"WaybillNumber":nul1,<br>"TrackingNumber":nul1,<br>"SubscribeType": "0",<br>"QueryNumber":"2",<br>"SubscribeTime":"2021-06-25T14:27:47",<br>"Subscriber":"2190"<br>"Code":"0000",</td>
</tr><tr>
<td colspan="1" rowspan="1">＂Message":＂提交成功！”，<br>"RequestId" : "0HMFS6Q0D8BU0:00000002",<br>"TimeStamp": "2022-03-02T10:28:16.5379242+00:00"<br>}</td>
</tr></table>

<!-- 67/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

## 2.21 按运输方式订阅轨迹

接口功能描述

客户向云途发送按运输方式订阅轨迹请求，返回操作结果。

事件流

基本流：

A.客户端向OMS请求按运输方式订阅轨迹；

B.OMS验证客户端信息合法性

·检查请求数据的格式

C. OMS 验证完信息后，以RETURN的形式返回响应数据

接口地址

<u>http://ADDR/api/tracking/CreatedProductSubscribe</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">是否必填</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">display_mode</td>
<td colspan="1" rowspan="1">轨迹内容：<br>0 ：全程轨迹；<br>1 ：头程轨迹（国内<br>轨迹）；<br>2 ：末端轨迹（国外派送轨迹）；<br>3 ：隐藏轨迹（不显<br>示任何轨迹）；<br>4 ：电子预报信息＋末端轨迹（国外派送轨迹）</td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">轨迹内容：<br>0 ：全程轨迹；<br>1 ：头程轨迹（国内轨迹）；<br>2 ：末端轨迹（国外派送轨迹）；<br>3 ：隐藏轨迹（不显示任何轨迹）；4 ：电子预报信息</td>
</tr><tr>
<td colspan="1" rowspan="1">product_info</td>
<td colspan="1" rowspan="1">销售产品信息</td>
<td colspan="1" rowspan="1">product_info []</td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">产品信息</td>
</tr><tr>
<td colspan="1" rowspan="1">query_mode</td>
<td colspan="1" rowspan="1">查询单号：<br>1 ：运单号；<br>2 ：订单号；<br>4 ：跟踪号；<br>3 ：运单号、订单<br>号；<br>5 ：运单号、跟踪<br>号；</td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">否</td>
<td colspan="1" rowspan="1">查询单号：<br>1 ：运单号；<br>2 ：订单号；<br>4 ：跟踪号；<br>3 ：运单号、订单号；<br>5 ：运单号、跟踪号；<br>6 ：订单号、跟踪号；<br>7 ：运单号、订单号、跟踪号；</td>
</tr><tr>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1">6 ：订单号、跟踪<br>号；<br>7 ：运单号、订单<br>号、跟踪号；</td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">country_codes</td>
<td colspan="1" rowspan="1">国家</td>
<td colspan="1" rowspan="1">String[]</td>
<td colspan="1" rowspan="1">否</td>
<td colspan="1" rowspan="1">国家：为空即所有国家</td>
</tr></table>

<!-- 68/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**product_info[］类型说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">是否必填</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">product_name</td>
<td colspan="1" rowspan="1">产品名称</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">产品名称</td>
</tr><tr>
<td colspan="1" rowspan="1">product_code</td>
<td colspan="1" rowspan="1">产品代码</td>
<td colspan="1" rowspan="1">String </td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">产品代码</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">返回参数</td>
<td colspan="1" rowspan="1">objec t </td>
<td colspan="1" rowspan="1">状态：true-成功；false-失败</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">代码</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码（10 ：请求参数错误20 ：服务执行超时 1001 ：提交失败！0000 ：提交成功！1011 ：部分成功！其余都是系统自定义异<br>常）</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">结果描述</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">一般为返回的错误消息</td>
</tr><tr>
<td colspan="1" rowspan="1">Requestld </td>
<td colspan="1" rowspan="1">请求id </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回数据内容</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">时间戳</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

**Item 类型说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Success </td>
<td colspan="1" rowspan="1">boolean </td>
<td colspan="1" rowspan="1">状态：true-成功；false-失败</td>
</tr><tr>
<td colspan="1" rowspan="1">ErrorCode </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">0 成功1000 参数错误</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回的错误消息</td>
</tr><tr>
<td colspan="1" rowspan="1">Data </td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">返回数据内容</td>
</tr></table>

**Data 类型说明**：

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">NoCount </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">订阅失败的数量</td>
</tr><tr>
<td colspan="1" rowspan="1">OkCount </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">订阅成功数量</td>
</tr><tr>
<td colspan="1" rowspan="1">FailNumbers </td>
<td colspan="1" rowspan="1">string[]</td>
<td colspan="1" rowspan="1">订阅失败的业务ID </td>
</tr></table>

<!-- 69/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<!-- 70/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

**请求失败响应包字段说明：**

参考<u>【失败响应】</u><u>&nbsp;</u>详细说明。

# 消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/tracking/CreatedProductSubscribe</u>

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"display_mode":0,<br>"product_info":[<br>＂product_name":＂中美VIP 专线＂，<br>"product_code":"USZXVIP"<br>"product_name":"TEST",<br>"product_code":"PK00999"<br>],<br>"query  mode":7,<br>"country_codes":[<br>"DK",<br>"BE"<br>]<br>}</td>
</tr></table>

**成功响应数据包示例：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{<br>"Success":true,<br>"ErrorCode":0,<br>"Message":"",<br>"Data":{<br>"NoCount":0,<br>"OkCount":4,<br>"FailNumbers":[<br>]<br>"Code" : "0000",<br>＂Message":＂提交成功！"，<br>"RequestId" : "OHMFSOMONOKTS:00000002",<br>"TimeStamp": "2022-03-03T06:17:16.3208326+00:00"</td>
</tr></table>

<!-- 71/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

## 2.22 取消按运输方式订阅

接口功能描述

客户向云途发送取消按运输方式订阅请求，返回操作结果。

事件流

基本流：

A.客户端向OMS请求取消按运输方式订阅；

B.

客户端向OMS请求IOSS备案；

OMS验证客户端信息合法性

·检查请求数据的格式

OMS验证完信息后，以RETURN的形式返回响应数据

接口地址

<u>http://ADDR/api/tracking/CancelProductSubscribe</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">是否必填</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Ids </td>
<td colspan="1" rowspan="1">业务ID </td>
<td colspan="1" rowspan="1">string []</td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1">此ID 与获取到的运输方式订阅数据中的业务ID 一致</td>
</tr></table>

请求成功响应包字段说明：

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">返回参数</td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">返回参数</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">代码</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码（10 ：请求参数错误20 ：服务执行超时1001：提交失败！0000：提交成功！1011：部分成功！其余都是系统自定义异常）</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">结果描述</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回的消息</td>
</tr><tr>
<td colspan="1" rowspan="1">Requestld </td>
<td colspan="1" rowspan="1">请求id </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求id </td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">时间戳</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

Item 类型说明：

<!-- 72/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Success </td>
<td colspan="1" rowspan="1">boolean </td>
<td colspan="1" rowspan="1">状态：true-成功；false-失败</td>
</tr><tr>
<td colspan="1" rowspan="1">ErrorCode </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">0 成功1000 参数错误</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回的错误消息</td>
</tr><tr>
<td colspan="1" rowspan="1">Data </td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">返回数据内容</td>
</tr></table>

**Data类型说明**：

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">NoCouunt </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">失败取消的数量</td>
</tr><tr>
<td colspan="1" rowspan="1">OkCount </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">取消成功数量</td>
</tr><tr>
<td colspan="1" rowspan="1">FailNumbers </td>
<td colspan="1" rowspan="1">string[]</td>
<td colspan="1" rowspan="1">取消订阅失败的业务ID </td>
</tr></table>

## 请求失败响应包字段说明：

参考<u>【失败响应】</u><u>&nbsp;</u>详细说明。

# 消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/tracking/CancelProductSubscribe</u>

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Ids":[<br>"1",<br>"2"<br>"3"<br>]<br>}</td>
</tr></table>

成功响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{ "Success":tr<br>ue, "Message":"",<br>"ErrorCode":0,<br>"Data":<br>"OkCount" : 2,<br>"NoCount":1,<br>"FailNumbers":[<br>"1"<br>]<br>1 </td>
</tr><tr>
<td colspan="1" rowspan="1">},<br>"Code": "0000",<br>＂Message":＂提交成功＂，<br>"RequestId" : "0HM9GLB91V907:00000002",<br>"TimeStamp": "2021-06-16T09:48:09.151795+00:00"<br>}</td>
</tr></table>

<!-- 73/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

<!-- 74/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

## 2.23 获取按运输方式订阅的数据

接口功能描述

客户向云途发送获取按运输方式订阅的数据请求，返回按运输方式轨迹订阅列表。事件流

基本流：

A.客户端向OMS请求按运输方式订阅的数据

B.OMS验证客户端信息合法性

·检查请求数据的格式

C.OMS验证完信息后，以RETURN的形式返回响应数据

接口地址

<u>http://ADDR/api/tracking/GetProductSubscribe</u>

协议定义

**请求包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">是否必填</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">display_mode</td>
<td colspan="1" rowspan="1">订阅类型</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">否</td>
<td colspan="1" rowspan="1">轨迹内容：<br>0 ：全程轨迹；<br>1 ：头程轨迹（国内轨迹）；2 ：末端轨迹（国外派送<br>轨迹）；<br>3 ：隐藏轨迹（不显示任<br>何轨迹）；<br>4 ：电子预报信息</td>
</tr><tr>
<td colspan="1" rowspan="1">product_code</td>
<td colspan="1" rowspan="1">产品代码</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">否</td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">pageCount </td>
<td colspan="1" rowspan="1">页数</td>
<td colspan="1" rowspan="1">Int </td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1"></td>
</tr><tr>
<td colspan="1" rowspan="1">pageIndex </td>
<td colspan="1" rowspan="1">当前页</td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">是</td>
<td colspan="1" rowspan="1"></td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">描述</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">返回参数</td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">状态：true-成功；false-失败</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">代码</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码（10 ：请求参数错误20 ：服务执行超时1001 ：提交失败！<br>0000 ：提交成功！1011 ：部分成功！其余都是系统自定义异常）</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">结果描述</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">一般为返回的错误消息</td>
</tr><tr>
<td colspan="1" rowspan="1">Requestld </td>
<td colspan="1" rowspan="1">请求id </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">返回数据内容</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">时间戳</td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr></table>

<!-- 75/82 -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 云途物流 EXPRESS -->

Item类型说明**：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Success </td>
<td colspan="1" rowspan="1">bool </td>
<td colspan="1" rowspan="1">状态：true-成功；false-失败</td>
</tr><tr>
<td colspan="1" rowspan="1">Data </td>
<td colspan="1" rowspan="1">object </td>
<td colspan="1" rowspan="1">单号订阅信息</td>
</tr></table>

**Data 类型说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">CountNum </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">查询总数</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">object[]</td>
<td colspan="1" rowspan="1">订阅信息</td>
</tr></table>

**Data对象Item类型说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">字段名</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Id </td>
<td colspan="1" rowspan="1">long </td>
<td colspan="1" rowspan="1">订阅Id </td>
</tr><tr>
<td colspan="1" rowspan="1">UserCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">客户编码</td>
</tr><tr>
<td colspan="1" rowspan="1">ProductCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">销售产品代码</td>
</tr><tr>
<td colspan="1" rowspan="1">ProductName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">销售产品名称</td>
</tr><tr>
<td colspan="1" rowspan="1">Status </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">状态0 未订阅1 已订阅</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryCode </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">国家二字简码</td>
</tr><tr>
<td colspan="1" rowspan="1">CountryName </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">国家名称</td>
</tr><tr>
<td colspan="1" rowspan="1">SubscribeType </td>
<td colspan="1" rowspan="1">int </td>
<td colspan="1" rowspan="1">订阅类型（轨迹内容）：<br>0 ：全程轨迹；<br>1 ：头程轨迹（国内轨迹）；<br>2 ：末端轨迹（国外派送轨迹）；<br>3 ：隐藏轨迹（不显示任何轨迹）；<br>4 ：电子预报信息＋末端轨迹（国外派送轨迹）</td>
</tr><tr>
<td colspan="1" rowspan="1">QueryNumber </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">查询单号：<br>1 ：运单号；<br>2 ：订单号；<br>4 ：跟踪号；<br>3 ：运单号、订单号；<br>5 ：运单号、跟踪号；<br>6 ：订单号、跟踪号；<br>7 ：运单号、订单号、跟踪号；</td>
</tr><tr>
<td colspan="1" rowspan="1">SubscribeTime </td>
<td colspan="1" rowspan="1">DateTime </td>
<td colspan="1" rowspan="1">订阅时间</td>
</tr><tr>
<td colspan="1" rowspan="1">Operator </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">订阅人</td>
</tr></table>

<!-- 76/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

**请求失败响应包字段说明：**

参考<u>【失败响应】</u>详细说明。

# 消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/WayBill/RegisterIoss</u>

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"product_code": "USZXVIP",<br>"pageIndex":1,<br>"pageCount":20,<br>"display_mode":"0"<br>}</td>
</tr></table>

成功响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item":{<br>"Success":true,<br>"Data":{<br>"CountNum":3,<br>"Item":[<br>{<br>"Id":2342,<br>"UserCode" :"C06901",<br>"ProductCode":"USZXVIP",<br>＂ProductName":＂中美VIP 专线＂，<br>"Status":1,<br>"SubscribeType":"0",<br>"QueryNumber":"7",<br>"CountryCode":"BE",<br>＂CountryName":＂比利时＂，<br>"Operator":"C06901",<br>"SubscribeTime": "2022-03-<br>03T14:17:08"<br>"Id":2244,<br>"UserCode" : "C06901",<br>"ProductCode" : "USZXVIP",<br>＂ProductName":＂中美VIP 专线＂，<br>"Status":1,<br>"SubscribeType":"0",<br>"QueryNumber":"7",<br>"CountryCode":"DK",<br>＂CountryName":＂丹麦＂，<br>"Operator" : "C06901",<br>"SubscribeTime":"2022-03-<br>03T14:17:07"</td>
</tr><tr>
<td colspan="1" rowspan="1">"Id":2245,<br>"UserCode" : "C06901",<br>"ProductCode": "USZXVIP",<br>"ProductName":nul1,<br>"Status":1,<br>"SubscribeType":"0",<br>"QueryNumber":"7",<br>"CountryCode" : "US",<br>＂CountryName":＂美国＂，<br>"Operator" : "C06901",<br>"SubscribeTime":"2021-08-<br>18T21:34:24"<br>]<br>},<br>"Code":"0000",<br>＂Message":＂提交成功！”，<br>"RequestId" : "OHMFSOMONOKU4:00000002",<br>"TimeStamp":"2022-03-03T06:18:39.9632838+00:00"</td>
</tr></table>

<!-- 77/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

## 2.24 IOSS号作废

接口功能描述

客户向云途请求IOSS号作废，返回处理状态。

事件流

基本流：

D.客户端向OMS请求IOSS号作废；

E.OMS验证客户端信息合法性

·检查请求数据的格式

F.OMS验证完信息后，以RETURN的形式返回响应数据

接口地址

<u>http://ADDR/api/WayBill/DisableIoss</u>

协议定义

**请求包字段说明：**

<!-- 78/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">长度</td>
<td colspan="1" rowspan="1">约束</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">50 </td>
<td colspan="1" rowspan="1">必填</td>
<td colspan="1" rowspan="1">IOSS 号备案返回的识别码</td>
</tr></table>

**请求成功响应包字段说明：**

<table border="1" ><tr>
<td colspan="1" rowspan="1">元素名称</td>
<td colspan="1" rowspan="1">类型</td>
<td colspan="1" rowspan="1">说明</td>
</tr><tr>
<td colspan="1" rowspan="1">Code </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果代码</td>
</tr><tr>
<td colspan="1" rowspan="1">Message </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">结果描述</td>
</tr><tr>
<td colspan="1" rowspan="1">RequestId </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">请求唯一标识</td>
</tr><tr>
<td colspan="1" rowspan="1">TimeStamp </td>
<td colspan="1" rowspan="1">string </td>
<td colspan="1" rowspan="1">时间戳</td>
</tr><tr>
<td colspan="1" rowspan="1">Item </td>
<td colspan="1" rowspan="1">bool </td>
<td colspan="1" rowspan="1">处理状态</td>
</tr></table>

**请求失败响应包字段说明：**

参考<u>【失败响应】</u><u>&nbsp;</u>详细说明。

消息示例

**请求数据包示例：**

POST 请求，数据类型请用json类型，请求数据为：

<u>http://ADDR/api/WayBill/DisableIoss</u>

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"code" : "IOSS231380908071723203831"<br>}</td>
</tr></table>

### 成功响应数据包示例：

<table border="1" ><tr>
<td colspan="1" rowspan="1">{<br>"Item": true,<br>"Code": "0000",<br>＂Message":＂提交成功！”，<br>"RequestId": "657659dcd453446897e9f074bb1b9f5c",<br>"TimeStamp": "2023-05-19T05:42:10.0586238+00:00"<br>}</td>
</tr></table>

<!-- 79/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

## 3．附录

### 3.1 XML解析规范

客户端的XML的节点值读取以节点名称的形式读取，勿用节点位置读取。

**3.2 结果代码表**

<table border="1" ><tr>
<td colspan="1" rowspan="1">结果代码</td>
<td colspan="1" rowspan="1">结果代码描述</td>
</tr><tr>
<td colspan="1" rowspan="1">0000 </td>
<td colspan="1" rowspan="1">提交成功</td>
</tr><tr>
<td colspan="1" rowspan="1">1001 </td>
<td colspan="1" rowspan="1">提交失败</td>
</tr><tr>
<td colspan="1" rowspan="1">1002 </td>
<td colspan="1" rowspan="1">接口 ApiKEy 或 ApiSecret 错误</td>
</tr><tr>
<td colspan="1" rowspan="1">1003 </td>
<td colspan="1" rowspan="1">签名错误</td>
</tr><tr>
<td colspan="1" rowspan="1">1004 </td>
<td colspan="1" rowspan="1">参数错误</td>
</tr><tr>
<td colspan="1" rowspan="1">1005 </td>
<td colspan="1" rowspan="1">手机号码错误</td>
</tr><tr>
<td colspan="1" rowspan="1">1006 </td>
<td colspan="1" rowspan="1">未找到数据</td>
</tr><tr>
<td colspan="1" rowspan="1">1011 </td>
<td colspan="1" rowspan="1">部分成功</td>
</tr><tr>
<td colspan="1" rowspan="1">2001 </td>
<td colspan="1" rowspan="1">日期格式错误</td>
</tr><tr>
<td colspan="1" rowspan="1">2002 </td>
<td colspan="1" rowspan="1">长度超过限制</td>
</tr><tr>
<td colspan="1" rowspan="1">2003 </td>
<td colspan="1" rowspan="1">客户编号不存在</td>
</tr><tr>
<td colspan="1" rowspan="1">2004 </td>
<td colspan="1" rowspan="1">订单号存在重复</td>
</tr><tr>
<td colspan="1" rowspan="1">2005 </td>
<td colspan="1" rowspan="1">订单号已存在</td>
</tr><tr>
<td colspan="1" rowspan="1">2006 </td>
<td colspan="1" rowspan="1">该运输方式没可用跟踪号</td>
</tr><tr>
<td colspan="1" rowspan="1">2007 </td>
<td colspan="1" rowspan="1">跟踪号已存在</td>
</tr><tr>
<td colspan="1" rowspan="1">2008 </td>
<td colspan="1" rowspan="1">未开通关税预付权限，请联系业务</td>
</tr><tr>
<td colspan="1" rowspan="1">2009 </td>
<td colspan="1" rowspan="1">重量必须大于零</td>
</tr><tr>
<td colspan="1" rowspan="1">2010 </td>
<td colspan="1" rowspan="1">该运输方式不发送到此国家</td>
</tr><tr>
<td colspan="1" rowspan="1">2011 </td>
<td colspan="1" rowspan="1">申报信息备注格式不正确</td>
</tr><tr>
<td colspan="1" rowspan="1">2012 </td>
<td colspan="1" rowspan="1">收件人电话格式不正确</td>
</tr><tr>
<td colspan="1" rowspan="1">2013 </td>
<td colspan="1" rowspan="1">订单号只能由数字和字母组成</td>
</tr><tr>
<td colspan="1" rowspan="1">9999 </td>
<td colspan="1" rowspan="1">平台异常</td>
</tr><tr>
<td colspan="1" rowspan="1">10000 </td>
<td colspan="1" rowspan="1">请求超过次数</td>
</tr><tr>
<td colspan="1" rowspan="1">2019 </td>
<td colspan="1" rowspan="1">注册失败</td>
</tr><tr>
<td colspan="1" rowspan="1">2020 </td>
<td colspan="1" rowspan="1">用户名不能为空且只能为数字和字母</td>
</tr><tr>
<td colspan="1" rowspan="1">2021 </td>
<td colspan="1" rowspan="1">用户密码输入为空</td>
</tr><tr>
<td colspan="1" rowspan="1">2022 </td>
<td colspan="1" rowspan="1">用户确认密码输入不一致</td>
</tr><tr>
<td colspan="1" rowspan="1">2023 </td>
<td colspan="1" rowspan="1">联系人不能为空</td>
</tr><tr>
<td colspan="1" rowspan="1">2024 </td>
<td colspan="1" rowspan="1">联系人手机不能为空</td>
</tr><tr>
<td colspan="1" rowspan="1">2025 </td>
<td colspan="1" rowspan="1">客户名称不能为空</td>
</tr><tr>
<td colspan="1" rowspan="1">2026 </td>
<td colspan="1" rowspan="1">Email 不能为空</td>
</tr><tr>
<td colspan="1" rowspan="1">2027 </td>
<td colspan="1" rowspan="1">地址不能为空</td>
</tr><tr>
<td colspan="1" rowspan="1">2028 </td>
<td colspan="1" rowspan="1">平台来源不能为空</td>
</tr><tr>
<td colspan="1" rowspan="1">2029 </td>
<td colspan="1" rowspan="1">用户名已存在</td>
</tr><tr>
<td colspan="1" rowspan="1">5001 </td>
<td colspan="1" rowspan="1">处理成功</td>
</tr><tr>
<td colspan="1" rowspan="1">5002 </td>
<td colspan="1" rowspan="1">单号类型［Type ］不存在</td>
</tr><tr>
<td colspan="1" rowspan="1">5003 </td>
<td colspan="1" rowspan="1">单号不能为空</td>
</tr><tr>
<td colspan="1" rowspan="1">5004 </td>
<td colspan="1" rowspan="1">单号不存在</td>
</tr><tr>
<td colspan="1" rowspan="1">5005 </td>
<td colspan="1" rowspan="1">订单不存在</td>
</tr><tr>
<td colspan="1" rowspan="1">5006 </td>
<td colspan="1" rowspan="1">拦截原因不能为空</td>
</tr><tr>
<td colspan="1" rowspan="1">5007 </td>
<td colspan="1" rowspan="1">订单不是已提交或已收货状态</td>
</tr><tr>
<td colspan="1" rowspan="1">5008 </td>
<td colspan="1" rowspan="1">订单不是已提交状态</td>
</tr><tr>
<td colspan="1" rowspan="1">5009 </td>
<td colspan="1" rowspan="1">拦截失败，请重试！</td>
</tr><tr>
<td colspan="1" rowspan="1">5010 </td>
<td colspan="1" rowspan="1">删除失败，请重试！</td>
</tr><tr>
<td colspan="1" rowspan="1">5011 </td>
<td colspan="1" rowspan="1">拦截成功！</td>
</tr><tr>
<td colspan="1" rowspan="1">5012 </td>
<td colspan="1" rowspan="1">删除成功</td>
</tr><tr>
<td colspan="1" rowspan="1">5099 </td>
<td colspan="1" rowspan="1">处理失败，请重试！</td>
</tr></table>

<!-- 80/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

**3.3 危险品代码表**

<table border="1" ><tr>
<td colspan="1" rowspan="1">危险品代码</td>
<td colspan="1" rowspan="1">危险品代码描述</td>
</tr><tr>
<td colspan="1" rowspan="1">810 </td>
<td colspan="1" rowspan="1">HAZMAT Air Eligible Ethanol Package Air Eligible Ethanol Packag </td>
</tr><tr>
<td colspan="1" rowspan="1">811 </td>
<td colspan="1" rowspan="1">HAZMAT Class 1 - Toy Propellant/Safety Fuse Package </td>
</tr><tr>
<td colspan="1" rowspan="1">812 </td>
<td colspan="1" rowspan="1">HAZMAT Class 3- Flammable Liquid Package </td>
</tr><tr>
<td colspan="1" rowspan="1">813 </td>
<td colspan="1" rowspan="1">HAZMAT Class 7 - Radioactive Materials Package </td>
</tr><tr>
<td colspan="1" rowspan="1">814 </td>
<td colspan="1" rowspan="1">HAZMAT Class 8 - Corrosive Materials Package </td>
</tr><tr>
<td colspan="1" rowspan="1">815 </td>
<td colspan="1" rowspan="1">HAZMAT Class 8 - Nonspillable Wet Battery Package </td>
</tr><tr>
<td colspan="1" rowspan="1">816 </td>
<td colspan="1" rowspan="1">HAZMAT Class 9 - Lithium Battery Marked - Ground Only Package </td>
</tr><tr>
<td colspan="1" rowspan="1">817 </td>
<td colspan="1" rowspan="1">HAZMAT Class 9 - Lithium Battery - Returns Package </td>
</tr><tr>
<td colspan="1" rowspan="1">818 </td>
<td colspan="1" rowspan="1">HAZMAT Class 9 - Lithium batteries,marked package </td>
</tr><tr>
<td colspan="1" rowspan="1">819 </td>
<td colspan="1" rowspan="1">HAZMAT Class 9 - Dry Ice Package </td>
</tr><tr>
<td colspan="1" rowspan="1">820 </td>
<td colspan="1" rowspan="1">HAZMAT Class 9 - Lithium batteries, unmarked package </td>
</tr><tr>
<td colspan="1" rowspan="1">821 </td>
<td colspan="1" rowspan="1">HAZMAT Class 9 - Magnetized Materials Package </td>
</tr><tr>
<td colspan="1" rowspan="1">822 </td>
<td colspan="1" rowspan="1">HAZMAT Division 4.1 - Flammable Solids or Safety Matches<br>Package </td>
</tr><tr>
<td colspan="1" rowspan="1">823 </td>
<td colspan="1" rowspan="1">HAZMAT Division 5.1 - Oxidizers Package </td>
</tr><tr>
<td colspan="1" rowspan="1">824 </td>
<td colspan="1" rowspan="1">HAZMAT Division 5.2 - Organic Peroxides Package </td>
</tr><tr>
<td colspan="1" rowspan="1">825 </td>
<td colspan="1" rowspan="1">HAZMAT Division 6.1 - Toxic Materials Package </td>
</tr><tr>
<td colspan="1" rowspan="1">826 </td>
<td colspan="1" rowspan="1">HAZMAT Division 6.2 - Infectious Substances Package </td>
</tr><tr>
<td colspan="1" rowspan="1">827 </td>
<td colspan="1" rowspan="1">HAZMAT Excepted Quantity Provision Package </td>
</tr><tr>
<td colspan="1" rowspan="1">828 </td>
<td colspan="1" rowspan="1">HAZMAT Ground Only </td>
</tr><tr>
<td colspan="1" rowspan="1">829 </td>
<td colspan="1" rowspan="1">HAZMAT ID8000 Consumer Commodity Package </td>
</tr><tr>
<td colspan="1" rowspan="1">830 </td>
<td colspan="1" rowspan="1">HAZMAT Lighters Package </td>
</tr><tr>
<td colspan="1" rowspan="1">831 </td>
<td colspan="1" rowspan="1">HAZMAT LTD QTY Ground Package </td>
</tr><tr>
<td colspan="1" rowspan="1">832 </td>
<td colspan="1" rowspan="1">HAZMAT Small Quantity Provision Package </td>
</tr></table>

<!-- 81/82 -->

<!-- 云途物流 EXPRESS -->

<!-- 云途物流API接口开发规范（OMS版） -->

<!-- 82/82 -->

