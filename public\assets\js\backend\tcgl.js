define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'tcgl/index' + location.search,
                    add_url: 'tcgl/add',
                    edit_url: 'tcgl/edit',
                    del_url: 'tcgl/del',
                    multi_url: 'tcgl/multi',
                    import_url: 'tcgl/import',
                    table: 'tcgl',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'admin.nickname', title: __('Admin.nickname'), operate: 'LIKE'},
						{field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange',datetimeFormat:"YYYY-MM-DD", autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'wls', title: __('Wls'), operate: 'LIKE'},
                        {field: 'wldh', title: __('Wldh'), operate: 'LIKE'},
                        {field: 'fhtime', title: __('Fhtime'), operate:'RANGE', addclass:'datetimerange',datetimeFormat:"YYYY-MM-DD", autocomplete:false, formatter: Table.api.formatter.datetime},
                        
                        
                       {
                           field: 'operate',
                           width: "150px",
                           title: __('Operate'),
                           table: table,
                           events: Table.api.events.operate,
                           buttons: [
                               
									{
										name: 'detail',
										text: __('详情'),
										title: __('详情'),
										classname: 'btn btn-xs btn-primary btn-dialog',
										icon: 'fa fa-list',
										extend:'data-area=["90%","90%"]',
										url: 'tcgl/detail',
										callback: function (data) {
										Layer.msg("订单已发货");
										return false;
										}
									}
                       		
                           ],
                           formatter: Table.api.formatter.operate
                       }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
