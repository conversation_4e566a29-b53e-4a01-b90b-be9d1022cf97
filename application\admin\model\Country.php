<?php

namespace app\admin\model;

use think\Model;


class Country extends Model
{





    // 表名
    protected $name = 'country';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'wl_type_text'
    ];

    public function getWlTypeTextAttr($value,$data){
        if(!isset($data['wl_type']) || $data['wl_type'] == -1 || $data['wl_type'] === null){
            return '未绑定';
        }else{
            $return = config('site.wls');
            return isset($return[$data['wl_type']]) ? $return[$data['wl_type']] : '未知';
        }
    }

    /**
     * 获取物流类型列表
     */
    public function getWlTypeList()
    {
        $wlsList = config('site.wls');
        $result = [];
        foreach ($wlsList as $key => $value) {
            $result[$key] = $value;
        }
        return $result;
    }
}
