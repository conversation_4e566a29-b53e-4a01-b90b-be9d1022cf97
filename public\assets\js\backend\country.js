define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'layer'], function ($, undefined, Backend, Table, Form, Layer) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'country/index' + location.search,
                    add_url: 'country/add',
                    edit_url: 'country/edit',
                    del_url: 'country/del',
                    multi_url: 'country/multi',
                    import_url: 'country/import',
                    bindwl_url: 'country/bindwl',
                    table: 'country',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'ename', title: __('Ename'), operate: 'LIKE'},
                        {field: 'sxname', title: __('Sxname'), operate: 'LIKE'},
                        {field: 'deliver_days', title: __('Deliver_days')},
                        {field: 'wl_type_text', title: __('wl_type_text')},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'bindwl',
                                    text: __('绑定物流'),
                                    title: __('绑定物流'),
                                    icon: 'fa fa-truck',
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    url: 'country/bindwl?id={id}',
                                    extend: 'data-area=\'["80%", "75%"]\''
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 我们已经在按钮定义中添加了URL参数，不需要这个自定义事件处理了
            /*
            $(document).on("click", ".btn-bindwl", function () {
                var that = this;
                var row = table.bootstrapTable('getRowByUniqueId', $(that).data('row-id'));
                var id = row.id;
                console.log("绑定物流按钮点击，行ID:", id, "行数据:", row);

                var url = 'country/bindwl?id=' + id;
                Fast.api.open(url, __('绑定物流'), {
                    area: ['500px', '400px'],
                    callback: function (data) {
                        // 刷新表格
                        table.bootstrapTable('refresh');
                    }
                });
                return false;
            });
            */
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
