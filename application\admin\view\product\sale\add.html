<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$name|default=''}">
        </div>
    </div>
	<div class="form-group">
	    <label class="control-label col-xs-12 col-sm-2">{:__('Images')}:</label>
	    <div class="col-xs-12 col-sm-8">
	        <div class="input-group">
	            <input id="c-images" data-rule="required" class="form-control" size="50" name="row[images]" type="text" value="{$images|default=''}">
	            <div class="input-group-addon no-border no-padding">
	                <span><button type="button" id="faupload-images" class="btn btn-danger faupload" data-input-id="c-images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
	                <span><button type="button" id="fachoose-images" class="btn btn-primary fachoose" data-input-id="c-images" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
	            </div>
	            <span class="msg-box n-right" for="c-images"></span>
	        </div>
	        <ul class="row list-inline faupload-preview" id="p-images"></ul>
	    </div>
	</div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Stylelist')}:</label>
        <div class="col-xs-12 col-sm-8">

                <div class="input-group">
                    <input id="c-files" data-rule="required" class="form-control" size="50" name="row[stylelist]" type="text" value="">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" id="plupload-files" class="btn btn-danger plupload" data-input-id="c-files" data-mimetype="*" data-multiple="true" data-preview-id="p-files"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        <span><button type="button" id="fachoose-files" class="btn btn-primary fachoose" data-input-id="c-files" data-mimetype="*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                    </div>
                    <span class="msg-box n-right" for="c-files"></span>
                </div>

                <!--ul需要添加 data-template和data-name属性，并一一对应且唯一 -->
                <ul class="row list-inline plupload-preview" id="p-files" data-template="introtpl" data-name="row[stylename]"></ul>

                <!--请注意 ul和textarea间不能存在其它任何元素，实际开发中textarea应该添加个hidden进行隐藏-->
                <textarea name="row[stylename]" class="form-control hidden" style="margin-top:5px;"></textarea>

                <!--这里自定义图片预览的模板 开始-->
                <script type="text/html" id="introtpl">
                    <li class="col-xs-3">
                        <a href="<%=fullurl%>" data-url="<%=url%>" target="_blank" class="thumbnail">
                            <img src="<%=fullurl%>" class="img-responsive">
                        </a>
                        <input type="text" name="row[intro][<%=index%>]" class="form-control mb-1" placeholder="请输入文件描述" value="<%=value?value:''%>"/>
                        <a href="javascript:;" class="btn btn-danger btn-xs btn-trash"><i class="fa fa-trash"></i></a>
                    </li>
                </script>
                <!--这里自定义图片预览的模板 结束-->

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Product_design_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-product_design_id" data-rule="required" data-params='{"custom[status]":"0"}' data-source="product/design/index" class="form-control selectpage" name="row[product_design_id]" type="text" value="{$product_design_id|default=''}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" data-source="store/index" class="form-control selectpage" name="row[store_id]" type="text" value="{$store_id|default=''}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Info')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-info" class="form-control" name="row[info]" type="text">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<script>
    // 在页面加载完成后，如果有图片，则自动预览
    $(document).ready(function() {
        // 处理图片预览
        var images = $("#c-images").val();
        if (images) {
            // 触发图片预览
            $("#c-images").trigger("fa.upload.success", [images]);
        }
    });
</script>
