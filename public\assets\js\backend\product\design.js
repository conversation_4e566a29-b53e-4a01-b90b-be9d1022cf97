define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'layer', 'fast', 'toastr'], function ($, undefined, Backend, Table, Form, Layer, Fast, Toastr) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'product/design/index' + location.search,
                    add_url: 'product/design/add',
                    edit_url: 'product/design/edit',
                    del_url: 'product/design/del',
                    multi_url: 'product/design/multi',
                    import_url: 'product/design/import',
                    table: 'product_design',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                templateView: true,
                templateFormatter: function (data, i, row) {
                    return '<div class="design-card">' +
                        '<div class="design-image"><img src="' + (data.images ? data.images.split(',')[0] : '') + '" alt=""></div>' +
                        '<div class="design-info">' +
                        '<div class="design-info-row"><span class="info-label">款式名称：</span><span class="info-value">' + data.name + '</span></div>' +
                        '<div class="design-info-row"><span class="info-label">录入账户：</span><span class="info-value">' + (data.admin ? data.admin.nickname : '') + '</span></div>' +
                        '<div class="design-info-row"><span class="info-label">设计人员：</span><span class="info-value">' + (data.designer ? data.designer.name : '') + '</span></div>' +
                        '<div class="design-info-row"><span class="info-label">绑定店铺：</span><span class="info-value">' + (data.store ? data.store.name : '') + '</span></div>' +
                        '<div class="design-info-row"><span class="info-label">款式说明：</span><span class="info-value">' + data.info + '</span></div>' +
                        '<div class="design-info-row"><span class="info-label">奖励状态：</span><span class="info-value">' + (data.status == 1 ? '已奖励' : '未奖励') + '</span></div>' +
                        '<div class="design-info-row"><span class="info-label">设计提报：</span><span class="info-value">' + Table.api.formatter.datetime(data.createtime) + '</span></div>' +
                        '<div class="design-info-row"><span class="info-label">绑定时间：</span><span class="info-value">' + Table.api.formatter.datetime(data.updatetime) + '</span></div>' +
                        '</div>' +
                        '<div class="design-actions">' +
                        '<a href="javascript:;" class="btn btn-action btn-edit" data-toggle="tooltip" data-table-id="table" title="编辑" data-pk="' + data.id + '">编辑</a>' +
                        '<a href="javascript:;" class="btn btn-action btn-del" data-toggle="tooltip" data-table-id="table" title="删除" data-pk="' + data.id + '">删除</a>' +
                        '<a href="javascript:;" class="btn btn-action btn-bind" data-toggle="tooltip" data-table-id="table" title="绑定" data-pk="' + data.id + '">绑定</a>' +
                        (data.status == 1 ? '<a href="javascript:;" class="btn btn-action btn-preview" data-toggle="tooltip" title="预览" data-id="' + data.id + '">预览</a>' : '') +
                        '</div>' +
                        '</div>';
                },
                templateParentClass: "design-card-container",
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'admin.nickname', title: __('Admin.nickname'), operate: 'LIKE'},
                        {field: 'designer.name', title: __('Designer.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'store.name', title: __('Store.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        // {field: 'store_id', title: __('Store_id')},
                        // {field: 'designer_id', title: __('Designer_id')},
                        {field: 'images', title: __('Images'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.images},
                        {field: 'info', title: __('Info'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
						{field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},

                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定编辑按钮事件
            $(document).on('click', '.btn-edit', function () {
                var id = $(this).data('pk');
                var url = 'product/design/edit';
                if (id) {
                    url = url + (url.indexOf('?') > -1 ? '&ids=' : '?ids=') + id;
                }
                Fast.api.open(url, __('Edit'), {
                    callback: function (data) {
                        table.bootstrapTable('refresh');
                    }
                });
            });

            // 绑定删除按钮事件
            $(document).on('click', '.btn-del', function () {
                var id = $(this).data('pk');
                var url = 'product/design/del';
                if (id) {
                    Layer.confirm(
                        __('Are you sure you want to delete this item?'),
                        {icon: 3, title: __('Warning'), shadeClose: true},
                        function (index) {
                            $.ajax({
                                url: url,
                                type: 'post',
                                dataType: 'json',
                                data: {ids: id},
                                success: function (data) {
                                    if (data.code === 1) {
                                        Toastr.success(data.msg);
                                        table.bootstrapTable('refresh');
                                    } else {
                                        Toastr.error(data.msg);
                                    }
                                    Layer.close(index);
                                },
                                error: function (xhr, status, error) {
                                    Toastr.error(__('Network error'));
                                    Layer.close(index);
                                }
                            });
                        }
                    );
                }
            });

            // 绑定绑定按钮事件
            $(document).on('click', '.btn-bind', function () {
                var id = $(this).data('pk');
                Fast.api.open('product/sale/add?ids=' + id, __('绑定'), {
                    callback: function (data) {
                        table.bootstrapTable('refresh');
                    }
                });
            });

            // 绑定预览按钮事件
            $(document).on('click', '.btn-preview', function () {
                var id = $(this).data('id');
                var index = Layer.open({
                    type: 1,
                    title: '产品设计预览',
                    area: ['800px', '600px'],
                    content: '<div class="loading">加载中...</div>',
                    success: function (layero) {
                        // 加载详细信息和款式列表
                        $.ajax({
                            url: 'product/design/preview',
                            type: 'get',
                            data: {id: id},
                            dataType: 'json',
                            success: function (data) {
                                if (data && data.code === 1) {
                                    var item = data.data.design;
                                    var styleImages = data.data.styleImages || [];

                                    var html = '<div class="design-preview">';
                                    html += '<div class="preview-header">';
                                    html += '<h3>' + item.name + '</h3>';
                                    html += '<p>设计人员：' + (item.designer ? item.designer.name : '') + '</p>';
                                    html += '<p>所属店铺：' + (item.store ? item.store.name : '') + '</p>';
                                    html += '</div>';

                                    // 预览区域 - 左侧缩略图，右侧大图
                                    html += '<div class="preview-container">';

                                    // 左侧缩略图列表
                                    html += '<div class="preview-thumbnails">';
                                    for (var i = 0; i < styleImages.length; i++) {
                                        html += '<div class="thumbnail-item' + (i === 0 ? ' active' : '') + '" data-index="' + i + '">';
                                        html += '<img src="' + styleImages[i] + '" alt="Style ' + (i+1) + '">';
                                        html += '<div class="thumbnail-label">Set ' + (i+1) + '</div>';
                                        html += '</div>';
                                    }
                                    html += '</div>';

                                    // 右侧大图
                                    html += '<div class="preview-main-image">';
                                    if (styleImages.length > 0) {
                                        html += '<img src="' + styleImages[0] + '" alt="Main Preview">';
                                    } else {
                                        html += '<div class="no-image">无可用图片</div>';
                                    }
                                    html += '</div>';

                                    html += '</div>'; // 结束 preview-container

                                    // 显示信息
                                    html += '<div class="preview-info">';
                                    html += '<p>款式说明：' + (item.info || '') + '</p>';
                                    html += '<p>奖励状态：' + (item.status == 1 ? '已奖励' : '未奖励') + '</p>';
                                    html += '<p>创建时间：' + Table.api.formatter.datetime(item.createtime) + '</p>';
                                    html += '<p>更新时间：' + Table.api.formatter.datetime(item.updatetime) + '</p>';
                                    html += '</div>';

                                    html += '</div>'; // 结束 design-preview

                                    // 添加预览样式
                                    var style = '<style>';
                                    style += '.design-preview { padding: 20px; }';
                                    style += '.preview-header { margin-bottom: 20px; }';
                                    style += '.preview-header h3 { margin-top: 0; font-weight: bold; }';

                                    // 预览容器样式
                                    style += '.preview-container { display: flex; background-color: #aaa; padding: 10px; margin-bottom: 20px; }';

                                    // 左侧缩略图列表样式
                                    style += '.preview-thumbnails { width: 120px; height: 400px; overflow-y: auto; background-color: #ccc; padding: 5px; }';
                                    style += '.thumbnail-item { margin-bottom: 10px; cursor: pointer; text-align: center; }';
                                    style += '.thumbnail-item img { width: 100px; height: 80px; object-fit: contain; border: 2px solid transparent; }';
                                    style += '.thumbnail-item.active img { border-color: #1890ff; }';
                                    style += '.thumbnail-label { font-size: 12px; margin-top: 2px; }';

                                    // 右侧大图样式
                                    style += '.preview-main-image { flex: 1; display: flex; align-items: center; justify-content: center; background-color: #fff; margin-left: 10px; }';
                                    style += '.preview-main-image img { max-width: 100%; max-height: 400px; object-fit: contain; }';
                                    style += '.no-image { color: #999; font-size: 16px; }';

                                    // 信息区域样式
                                    style += '.preview-info { padding: 10px; background-color: #f5f5f5; border-radius: 4px; }';
                                    style += '.preview-info p { margin-bottom: 5px; }';
                                    style += '</style>';

                                    $(layero).find('.layui-layer-content').html(style + html);

                                    // 绑定缩略图点击事件
                                    $(layero).find('.thumbnail-item').on('click', function() {
                                        var index = $(this).data('index');
                                        var imageUrl = styleImages[index];

                                        // 更新大图
                                        $(layero).find('.preview-main-image img').attr('src', imageUrl);

                                        // 更新选中状态
                                        $(layero).find('.thumbnail-item').removeClass('active');
                                        $(this).addClass('active');
                                    });
                                } else {
                                    $(layero).find('.layui-layer-content').html('<div class="text-center">无法加载设计信息</div>');
                                }
                            },
                            error: function() {
                                $(layero).find('.layui-layer-content').html('<div class="text-center">加载设计信息失败</div>');
                            }
                        });
                    }
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
