<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_id" data-rule="required" data-source="order/index" data-field="ddbh" class="form-control selectpage" name="row[order_id]" type="text" value="{$row.order_id|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Kstxt')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-kstxt" data-rule="required" class="form-control" name="row[kstxt]" type="text" value="{$row.kstxt|htmlentities}">
        </div>
    </div>
	<div class="form-group">
	    <label class="control-label col-xs-12 col-sm-2">{:__('Ksimages')}:</label>
	    <div class="col-xs-12 col-sm-8">
	        <div class="input-group">
	            <input id="c-images" data-rule="" class="form-control" size="50" name="row[ksimages]" type="text" value="{$row.ksimages|htmlentities}">
	            <div class="input-group-addon no-border no-padding">
	                <span><button type="button" id="faupload-images" class="btn btn-danger faupload" data-input-id="c-images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
	                <span><button type="button" id="fachoose-images" class="btn btn-primary fachoose" data-input-id="c-images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
	            </div>
	            <span class="msg-box n-right" for="c-images"></span>
	        </div>
	        <ul class="row list-inline faupload-preview" id="p-images"></ul>
	    </div>
	</div>
	<div class="form-group">
	    <label class="control-label col-xs-12 col-sm-2">{:__('Jltime')}:</label>
	    <div class="col-xs-12 col-sm-8">
	        <input id="c-jltime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[jltime]" type="text" value="{:$row.jltime?datetime($row.jltime):''}">
	    </div>
	</div>
    <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div> -->
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
