<form id="send-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	
	<div class="orderbox">
		<h3 class="center topname">订单补发</h3>
		<div class="conbox">
			<div class="conlist flex m30 item-center">
				<div class="itembox">
					{foreach name="$data" item="vo"}
					<div class="item flex item-center">
						<div class="select">
							<input type="checkbox" name="ids[]" value="{$key}">
						</div>
						<div class="img">
							<img src="{:$vo.skuimg}" width="160" alt="">
						</div>
						<div class="text">
							<p><span class="b">定制名称：</span>{:$vo.name}</p>
							<p><span class="b">背面刻字：</span>{:$vo.bmdk}</p>
							<p><span class="b">祝福打印：</span>{:$vo.zfdy}</p>
							<p>
								<span class="b">特殊要求：</span><span class="tsyq">{:$vo.tsyq}</span>
								{if $vo.tsyqimgs && $vo['tsyqimgs'][0]}
								{foreach name="$vo.tsyqimgs" item="vo2" key="key2"}
								<img src="{:$vo2}" class="proimg imgicon pointer" onclick="fangda('{:$vo2}');" alt="">
								{/foreach}
								{/if}
							</p>
							<p>
								{if $vo.sfdy}
								<span class="red btn">已打印</span>
								{else}
								<span class="btn" onclick="operate('{:$row.id}','{:$key}','打印');">打印</span>
								{/if}
								{if $vo.sfqg}
								<span class="red btn">已切割</span>
								{else}
								<span class="btn" onclick="operate('{:$row.id}','{:$key}','切割');">切割</span>
								{/if}
							</p>
						</div>
					</div>
					{/foreach}
				</div>
				<div class="addr">
					<p class="b">联系方式：</p>
					<p>{$row.lxfs}</p>
					<p class="b">发货地址：</p>
					<p>
						{$row.khname}<br>
						{$row.khaddr1}<br>
						{$row.city} {$row.state} {$row.zipcode}<br>
						{$row.country}<br>
					
					</p>
				</div>
				<div class="status">
					{if $row.ifjjlist}
					<span class="red btn">加急</span>
					{/if}
					{if $row.ifdblist}
					<span class="red btn">打包</span>
					{/if}
				</div>
			</div>
			
			<div class="fot">
				<p><span class="b">补发理由：</span></p>
				<textarea name="row[reason]"  data-rule="required" id="" style="width:100%;height:60px"></textarea>
			</div>	
			
		</div>
	</div>
    
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
<script>
	function copyText(text) {
		var input = document.getElementById("input");
		input.value = text; // 修改文本框的内容

		input.select(); // 选中文本

		document.execCommand("copy"); // 执行浏览器复制命令

		layer.msg("复制成功！");
	}
	
	function fangda(img){
		layer.open({
			type: 1,
			title: '图片',
			fix: true,
			shadeClose: true,
			shade: 0,
			area: ['500px', '500px'],
			content: "<img class='fdimg' src='"+img+"'>"
		});
	}
</script>

<style>
	.flex{display: flex;}
	.center{text-align: center;}
	.m30{margin:30px 0;}
	.b{font-weight: 900;}
	.conlist{justify-content: space-between;}
	.item-center{align-items: center;}
	.itembox{margin-right: 50px;}
	.itembox .item{padding: 20px 0;}
	.itembox .img img{margin-right: 50px;width:140px}
	.topname{margin: 30px auto;font-family: 900;font-size: 40px;}
	.itembox .text p{margin-bottom: 5px;}
	.conbox{padding:20px 50px;}
	.conbox .tit span{margin-right: 20px;padding: 10px 20px;background-color: #000;color: #fff;}
	.btn{padding: 3px 10px;color: #fff;background-color: #000;margin-right: 10px;}
	.btn.red{background-color: #f00;}
	.status span{display: block;margin: 20px 0;}
	.conlist{border:1px solid #ccc;padding:0 20px;}
	.fot{border:1px solid #ccc;padding: 20px;}
	.select{width: 30px;margin-right: 30px;}
	#input{position: absolute;top: 0;left: 0;opacity: 0;z-index: -10;}
	.imgicon{width:35px;height:35px;border: 1px solid #eee;border-radius: 3px;}
	.pointer{cursor: pointer;}
	.fdimg{height:calc(100% - 20px);width:auto;display: block;margin: 10px auto;max-width: 100%;max-height: calc(100% - 20px);}
</style>
