<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ddbh')}:</label>
        <div class="col-xs-12 col-sm-10">
            <input id="c-ddbh" class="form-control" name="row[ddbh]" type="text" value="{$row.ddbh|htmlentities}">
        </div>
    </div>
	<div class="form-group">
	    <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
	    <div class="col-xs-12 col-sm-10">
	        <input id="c-store_id" data-rule="required" data-source="store/index" class="form-control selectpage" name="row[store_id]" type="text" value="{$row.store_id|htmlentities}">
	    </div>
	</div>
	<div class="form-group">
	    <label class="control-label col-xs-12 col-sm-2">{:__('Datajson')}:</label>
	    <div class="col-xs-12 col-sm-10">

		   <table class="table table-responsive fieldlist" data-name="row[datajson]" data-template="testtpl" data-tag="tr">
		       <tr>
					<td>产品选择</td>
					<td>在售产品</td>
					<td>产品规格</td>
					<td>定制名称</td>
					<td>背面雕刻</td>
					<td>祝福打印</td>
					<td>特殊要求</td>
					<td>要求描述</td>
					<td>是否打印</td>
					<td>是否切割</td>
					<td></td>
		       </tr>
		       <tr>
		           <td colspan="5"><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a></td>
		       </tr>
		       <textarea name="row[datajson]" class="form-control hide" cols="30" rows="5">{$row.datajson|htmlentities}</textarea>
		   </table>
		   <!--定义模板，模板语法使用Art-Template模板语法-->
		   <script type="text/html" id="testtpl">
		       <tr class="form-inline pro<%=index%>">
		        <td>
					<button type="button" data-id="<%=index%>" class="btn btn-success selectpro">选择</button>
					<input type="hidden" name="row[<%=name%>][<%=index%>][sku]" class="sku" value="<%=row['sku']%>">
					<input type="hidden" name="row[<%=name%>][<%=index%>][proimg]" class="proimgtxt" value="<%=row['proimg']%>">
					<input type="hidden" name="row[<%=name%>][<%=index%>][skuimg]" class="skuimgtxt" value="<%=row['skuimg']%>">
					<input type="hidden" name="row[<%=name%>][<%=index%>][tsyqimgs]" class="tsyqimgstxt" value="<%=row['tsyqimgs']%>">
					<input type="hidden" name="row[<%=name%>][<%=index%>][bmdkcon]" class="bmdkcon" value="<%=row['bmdkcon']%>">
					<input type="hidden" name="row[<%=name%>][<%=index%>][zfdycon]" class="zfdycon" value="<%=row['zfdycon']%>">
				</td>
				<td><img src="<%if(row.proimg){%><%=row['proimg']%><%}else{%>/assets/img/imgicon.jpg<%}%>" class="proimg imgicon" alt=""></td>
				<td><img src="<%if(row.skuimg){%><%=row['skuimg']%><%}else{%>/assets/img/imgicon.jpg<%}%>" class="skuimg imgicon" alt=""></td>
		   		<td><input type="text" name="row[<%=name%>][<%=index%>][name]" class="form-control"  style="width:100%;" value="<%=row['name']%>"></td>
			   <td><input type="text" name="row[<%=name%>][<%=index%>][bmdk]" class="form-control bmdktxt" readonly  style="width:100px;" value="<%=row['bmdk']%>"><button type="button" data-id="<%=index%>" data-value="bmdk" class="btn btn-success btn-copy">背面雕刻</button></td>
			   <td><input type="text" name="row[<%=name%>][<%=index%>][zfdy]" class="form-control zfdytxt" readonly  style="width:100px;" value="<%=row['zfdy']%>"><button type="button" data-id="<%=index%>" data-value="zfdy" class="btn btn-success btn-copy">祝福打印</button></td>
			   <td>
				   <button type="button" data-id="<%=index%>" data-oid="{$row.id}" class="btn btn-success uploadtsyq">上传</button>
				   <span class="yqimgs">
					   <%if(row.tsyqimgs){%>
					   <%imgs = row.tsyqimgs.split(",")%>
					   <%for(i=0;i<imgs.length;i++){%>
						  <img src="<%=imgs[i]%>" class="imgicon" alt="">
					   <%}%>
					   <%}%>
				   </span>
				</td>
			   <td><input type="text" name="row[<%=name%>][<%=index%>][tsyq]" class="form-control"  style="width:100%;" value="<%=row['tsyq']%>"></td>
			   <td>
				   <select name="row[<%=name%>][<%=index%>][sfdy]" class="form-control selectpicker" id="">
						<option value="0" <%if(row.sfdy==0){%>selected<%}%> >未打印</option>
						<option value="1" <%if(row.sfdy==1){%>selected<%}%>	>已打印</option>
				   </select>
			   </td>
			   <td>
				   <select name="row[<%=name%>][<%=index%>][sfqg]" class="form-control selectpicker" id="">
						<option value="0" <%if(row.sfqg==0){%>selected<%}%> >未切割</option>
						<option value="1" <%if(row.sfqg==1){%>selected<%}%> >已切割</option>
				   </select>
			   </td>
			   <td><span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> </td>
		       </tr>
		   </script>


	    </div>
	</div>
   <!-- <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-10">

            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label>
            {/foreach}
            </div>

        </div>
    </div> -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ifjjlist')}:</label>
        <div class="col-xs-12 col-sm-10">

            <select  id="c-ifjjlist" data-rule="required" class="form-control selectpicker" name="row[ifjjlist]">
                {foreach name="ifjjlistList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.ifjjlist"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
	<div class="form-group">
	    <label class="control-label col-xs-12 col-sm-2">{:__('Ifdblist')}:</label>
	    <div class="col-xs-12 col-sm-10">

	        <select  id="c-ifdblist" data-rule="required" class="form-control selectpicker" name="row[ifdblist]">
	            {foreach name="ifdblistList" item="vo"}
	                <option value="{$key}" {in name="key" value="$row.ifdblist"}selected{/in}>{$vo}</option>
	            {/foreach}
	        </select>

	    </div>
	</div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Info')}:</label>
        <div class="col-xs-12 col-sm-10">
            <input id="c-info" class="form-control" name="row[info]" type="text" value="{$row.info|htmlentities}">
        </div>
    </div>
	<div class="form-group">
	    <label class="control-label col-xs-12 col-sm-2">{:__('联系方式')}:</label>
	    <div class="col-xs-12 col-sm-10">
	        <input id="c-lxfs" class="form-control" name="row[lxfs]" type="text" value="{$row.lxfs|htmlentities}">
	    </div>
	</div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ordertime')}:</label>
        <div class="col-xs-12 col-sm-10">
            <input id="c-ordertime" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm" data-use-current="true" name="row[ordertime]" type="text" value="{:$row.ordertime?datetime($row.ordertime):''}">
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-10">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<style>
	.col-sm-2{width:100px !important;}
	.col-sm-10{width:calc(100% - 150px) !important;}
	.imgicon{max-width:35px;max-height:35px;width:auto;height:auto;object-fit:contain;border: 1px solid #eee;border-radius: 3px;}
	.conbox{position: fixed;transform:translate(-50%,-50%);left:50%;top:50%;width:80%;height:auto;z-index: 999;display: none;background-color: #fff;padding: 20px;}
	.bg{position: fixed;top:0;left:0;width:100%;height:100%;background: rgba(0, 0, 0, .5);z-index: 888;display: none;}
</style>
<form id="copy-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
<div class="conbox">
		<input type="hidden" name="data[key]" id="keyid" value="">
		<input type="hidden" name="data[field]" id="field" value="">
    	<div class="conlist flex item-center" style="margin-bottom: 10px;">
    		<div class="itembox">
    			<textarea class="form-control editor0" name="data[fieldcon]" id="fieldcon" style="width:100%" rows="10"></textarea>
    		</div>
    	</div>
    	<button type="button" class="btn btn-primary btn-copy-submit" data-id="">{:__('OK')}</button>
    </div>
<div class="bg"></div>
</form>