<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>打印标签</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .print-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .order-info {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .order-info h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            width: 120px;
            color: #555;
        }
        .info-value {
            flex: 1;
            color: #333;
        }
        .print-actions {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            display: inline-block;
            padding: 12px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-size: 16px;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .print-preview {
            margin-top: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .print-preview h4 {
            margin-top: 0;
            color: #666;
        }
        .tracking-number {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            background-color: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            margin: 15px 0;
            letter-spacing: 2px;
        }
        @media print {
            body {
                margin: 0;
                background: white;
            }
            .print-container {
                box-shadow: none;
                margin: 0;
                padding: 20px;
            }
            .print-actions {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="order-info">
            <h3>订单信息</h3>
            <div class="info-row">
                <span class="info-label">订单编号:</span>
                <span class="info-value">{$row.ddbh}</span>
            </div>
            <div class="info-row">
                <span class="info-label">订单状态:</span>
                <span class="info-value">已准备发货</span>
            </div>
            <div class="info-row">
                <span class="info-label">物流商:</span>
                <span class="info-value">{$row.wls}</span>
            </div>
            <div class="info-row">
                <span class="info-label">物流单号:</span>
                <span class="info-value">{$row.wldh}</span>
            </div>
            <div class="info-row">
                <span class="info-label">收件人:</span>
                <span class="info-value">{$row.khname}</span>
            </div>
            <div class="info-row">
                <span class="info-label">收件地址:</span>
                <span class="info-value">{$row.khaddr1} {$row.khaddr2}</span>
            </div>
            <div class="info-row">
                <span class="info-label">城市/州:</span>
                <span class="info-value">{$row.city}, {$row.state}</span>
            </div>
            <div class="info-row">
                <span class="info-label">国家:</span>
                <span class="info-value">{$row.country}</span>
            </div>
            <div class="info-row">
                <span class="info-label">邮编:</span>
                <span class="info-value">{$row.zipcode}</span>
            </div>
        </div>

        <div class="print-preview">
            <h4>物流标签预览</h4>
            <div class="tracking-number">{$row.wldh}</div>
            <p><strong>收件人:</strong> {$row.khname}</p>
            <p><strong>地址:</strong> {$row.khaddr1} {$row.khaddr2}</p>
            <p><strong>城市:</strong> {$row.city}, {$row.state} {$row.zipcode}</p>
            <p><strong>国家:</strong> {$row.country}</p>
        </div>

        <div class="print-actions">
            {if $printUrl && $printUrl != '#'}
            <a href="{$printUrl}" target="_blank" class="btn btn-primary">
                <i class="fa fa-external-link"></i> 打开物流商打印页面
            </a>
            {/if}
            <button onclick="window.print()" class="btn btn-success">
                <i class="fa fa-print"></i> 打印此页面
            </button>
            <button onclick="parent.Layer.closeAll()" class="btn btn-secondary">
                <i class="fa fa-times"></i> 关闭
            </button>
        </div>
    </div>

    <script>
        // 自动聚焦到打印按钮
        document.addEventListener('DOMContentLoaded', function() {
            // 如果有物流商打印链接，优先显示
            var printLink = document.querySelector('a[href*="print"]');
            if (printLink && printLink.href !== '#') {
                printLink.focus();
            }
        });
    </script>
</body>
</html>
