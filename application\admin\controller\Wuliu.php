<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
/**
 * 物流管理
 *
 * @icon fa fa-circle-o
 */
class Wuliu extends Backend
{

    /**
     * Wuliu模型对象
     * @var \app\admin\model\Wuliu
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Wuliu;
        $this->view->assign("wllistList", $this->model->getWllistList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['admin','order','store'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('admin')->visible(['nickname']);
				$row->getRelation('order')->visible(['ddbh','wls','wldh']);
				$row->getRelation('store')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }
	
	/**
	 * 添加
	 *
	 * @return string
	 * @throws \think\Exception
	 */
	public function add()
	{
	    if (false === $this->request->isPost()) {
	        return $this->view->fetch();
	    }
	    $params = $this->request->post('row/a');
	    if (empty($params)) {
	        $this->error(__('Parameter %s can not be empty', ''));
	    }
	    $params = $this->preExcludeFields($params);
	
	    if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
	        $params[$this->dataLimitField] = $this->auth->id;
	    }
	    $result = false;
	    Db::startTrans();
	    try {
	        //是否采用模型验证
	        if ($this->modelValidate) {
	            $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
	            $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
	            $this->model->validateFailException()->validate($validate);
	        }
			$params['admin_id'] = $this->auth->id;
			$order_row = Db::table('fa_order')->where('id',$params['order_id'])->find();
			$params['store_id'] = $order_row['store_id'];
			
	        $result = $this->model->allowField(true)->save($params);
	        Db::commit();
	    } catch (ValidateException|PDOException|Exception $e) {
	        Db::rollback();
	        $this->error($e->getMessage());
	    }
	    if ($result === false) {
	        $this->error(__('No rows were inserted'));
	    }
	    $this->success();
	}
	
	/**
	 * 编辑
	 *
	 * @param $ids
	 * @return string
	 * @throws DbException
	 * @throws \think\Exception
	 */
	public function edit($ids = null)
	{
	    $row = $this->model->get($ids);
		$order_row = Db::table('fa_order')->where('id',$row['order_id'])->find();
	    if (!$row) {
	        $this->error(__('No Results were found'));
	    }
	    $adminIds = $this->getDataLimitAdminIds();
	    if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
	        $this->error(__('You have no permission'));
	    }
	    if (false === $this->request->isPost()) {
	        $this->view->assign('row', $row);
			$this->view->assign('order', $order_row);
	        return $this->view->fetch();
	    }
	    $params = $this->request->post('row/a');
	    if (empty($params)) {
	        $this->error(__('Parameter %s can not be empty', ''));
	    }
	    $params = $this->preExcludeFields($params);
	    $result = false;
	    Db::startTrans();
	    try {
	        //是否采用模型验证
	        if ($this->modelValidate) {
	            $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
	            $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
	            $row->validateFailException()->validate($validate);
	        }
			
			$order = $this->request->post('order/a');
			Db::table('fa_order')->where('id',$row['order_id'])->update($order);
			
			
	        $result = $row->allowField(true)->save($params);
	        Db::commit();
	    } catch (ValidateException|PDOException|Exception $e) {
	        Db::rollback();
	        $this->error($e->getMessage());
	    }
	    if (false === $result) {
	        $this->error(__('No rows were updated'));
	    }
	    $this->success();
	}
	
	/**
	 * 删除
	 *
	 * @param $ids
	 * @return void
	 * @throws DbException
	 * @throws DataNotFoundException
	 * @throws ModelNotFoundException
	 */
	public function del($ids = null)
	{
	    if (false === $this->request->isPost()) {
	        $this->error(__("Invalid parameters"));
	    }
	    $ids = $ids ?: $this->request->post("ids");
	    if (empty($ids)) {
	        $this->error(__('Parameter %s can not be empty', 'ids'));
	    }
	    $pk = $this->model->getPk();
	    $adminIds = $this->getDataLimitAdminIds();
	    if (is_array($adminIds)) {
	        $this->model->where($this->dataLimitField, 'in', $adminIds);
	    }
	    $list = $this->model->where($pk, 'in', $ids)->select();
	
	    $count = 0;
	    Db::startTrans();
	    try {
	        foreach ($list as $item) {
	            $count += $item->delete();
	        }
	        Db::commit();
	    } catch (PDOException|Exception $e) {
	        Db::rollback();
	        $this->error($e->getMessage());
	    }
	    if ($count) {
	        $this->success();
	    }
	    $this->error(__('No rows were deleted'));
	}
	
	public function detail($ids = null)
	{
	    $row = $this->model->get($ids);
	    if (!$row) {
	        $this->error(__('No Results were found'));
	    }
	    $adminIds = $this->getDataLimitAdminIds();
	    if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
	        $this->error(__('You have no permission'));
	    }
	    if (false === $this->request->isPost()) {
			$order_row = Db::table('fa_order')->where('id',$row['order_id'])->find();
			$datajson = $row['datajson'] ? json_decode($row['datajson'],true) : [];
	        $this->view->assign('datajson', $datajson);
			$this->view->assign('order', $order_row);
			$siteconfig = Db::table('fa_config')->where('name','wls')->find();
			$wls = json_decode($siteconfig['value'],true);
			if($order_row['wls'] == $wls[1]){
				$template = 'letian_detail';
			}else{
				$template = 'ubi_detail';
			}
			return $this->view->fetch($template);
	    }
	    
	}
	
	public function get_require($ids = '')
	{
		$order_rows = Db::table('fa_order')->where('status',3)->orderRaw('rand()')->limit(50)->select();
		$siteconfig = Db::table('fa_config')->where('name','wls')->find();
		$wls = json_decode($siteconfig['value'],true);
		if(count($order_rows)){
			foreach($order_rows as $k => $row){
				if($row['wls'] == $wls[1]){
					$return = $this->wls_letian($row['id'],$row['wldh']);
				}elseif($row['wls'] == $wls[0]){
					$return = $this->wls_ubi($row['id'],$row['wldh']);
				}
			}
		}
	    $this->success("更新成功");
	}
	
	public function get_require1($ids = ''){
		$return = $this->wls_letian(69,'9200190362718806138860');
	}
	
	// 物流:乐天
	public function wls_letian($ids = null, $wldh = null)
	{
		// $ids = 15;
		// $wldh = '9200190362718802660655';
		$postdata = [
			'appToken' => '3c84207e59310681afd80d1a8d0e4eb4',
			'appKey' => '144c1879509afaad44034f290f082aa7144c1879509afaad44034f290f082aa7',
			'serviceMethod' => 'gettrack',
		];
		$params = [
				'tracking_number' => $wldh,
		];
		$postdata['paramsJson'] = json_encode($params);
		$urlEncodedData = '';
		foreach ($postdata as $key => $value) {
		    // 使用 urlencode 函数对键和值进行 URL 编码
		    $encodedKey = urlencode($key);
		    $encodedValue = urlencode($value);
		    
		    // 拼接键值对，并用 & 符号分隔
		    if ($urlEncodedData !== '') {
		        $urlEncodedData .= '&';
		    }
		    $urlEncodedData .= $encodedKey . '=' . $encodedValue;
		}
		// print_r($urlEncodedData);exit;
		$curl = curl_init();
		curl_setopt_array($curl, array(
		   CURLOPT_URL => 'http://kehu.ltexp.com.cn/webservice/PublicService.asmx/ServiceInterfaceUTF8',
		   CURLOPT_RETURNTRANSFER => true,
		   CURLOPT_ENCODING => '',
		   CURLOPT_MAXREDIRS => 10,
		   CURLOPT_TIMEOUT => 0,
		   CURLOPT_FOLLOWLOCATION => true,
		   CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		   CURLOPT_CUSTOMREQUEST => 'POST',
		   CURLOPT_POSTFIELDS => $urlEncodedData,
		   CURLOPT_HTTPHEADER => array(
		      'User-Agent: Apifox/1.0.0 (https://apifox.com)'
		   ),
		));
		
		$response = curl_exec($curl);
		
		curl_close($curl);
		
		$response_arr = json_decode($response ,true);
		
		$wl_row = Db::table('fa_wuliu')->where('order_id',$ids)->find();
		$order_row = Db::table('fa_order')->where('id',$ids)->find();
		$wlid = $wl_row ? $wl_row['id'] : 0;
		if($response_arr['success'] == 1){
			if($wl_row){
				$country_row = Db::table('fa_country')->where('ename',strtoupper($order_row['country']))->find();
				$status = $wl_row['status'];
				$siteconfig = Db::table('fa_config')->where('name','rkdays')->find();
				if(time() - $order_row['fhtime'] > $siteconfig['value'] * 86400 && $status == 0){
					$status = 3;
				}
				if($response_arr['data'][0]['track_status'] == 'CC'){
					$status = 2;
				}elseif($response_arr['data'][0]['details'][0]['track_description'] == '包裹接收'){
					$status = 1;
				}else{
					$deliver_days = isset($country_row) && $country_row['deliver_days'] != 0 ? $country_row['deliver_days'] * 1 : 0;
					if($deliver_days){
						$wl_row['wllist'] = time() - $order_row['fhtime'] > $deliver_days * 86400 && $wl_row['wllist'] != 2 ? 1 : $wl_row['wllist']; 
						if(time() - $order_row['fhtime'] > $deliver_days * 86400 && $wl_row['wllist'] != 2){
							$status = 4;
						}
						
					}
					
				}
				// print_r($status);exit;
				Db::table('fa_wuliu')->where('order_id',$ids)->update([
					'wltxt' => $response_arr['data'][0]['track_status_name'],
					'datajson' => $response,
					'status' => $status,
					'wllist' => $wl_row['wllist'],
				]);
				$wlid = $wl_row['id'];
			}else{
				Db::table('fa_wuliu')->insert([
					'admin_id' => $this->auth->id,
					'order_id' => $ids,
					'store_id' => $order_row['store_id'],
					'wltxt' => $response_arr['data'][0]['track_status_name'],
					'datajson' => $response,
					'status' => $response_arr['data'][0]['details'][0]['track_description'] == '包裹接收' ? 1 : 0,
					'createtime' => time(),
					'updatetime' => time(),
				]);
				$wlid = Db::table('fa_wuliu')->getLastInsID();
			}
			
			if($response_arr['data'][0]['track_status'] == 'CC'){
				Db::table('fa_order')->where('id',$ids)->update(['status' => 6]);
				Db::table('fa_ordergoods')->where('order_id',$ids)->update(['status' => 6]);
			}
		}else{
			if(!$wl_row){
				Db::table('fa_wuliu')->insert([
					'admin_id' => $this->auth->id,
					'order_id' => $ids,
					'status' => 0,
					'createtime' => time(),
					'updatetime' => time(),
				]);
				$wlid = Db::table('fa_wuliu')->getLastInsID();
			}else{
				$siteconfig = Db::table('fa_config')->where('name','rkdays')->find();
				if(time() - $order_row['fhtime'] > $siteconfig['value'] * 86400){
					Db::table('fa_wuliu')->where('id',$wl_row['id'])->update([
						'status' => 3,
					]);
				}
			}
		}
		if($order_row['wuliu_id'] == false){
			Db::table('fa_order')->where('id',$ids)->update(['wuliu_id'=>$wlid]);
		}
		
	    // $this->success("更新成功");
	}
	
	public function wls_ubi($ids = null, $wldh = null)
	{
		$server = 'http://cn.etowertech.com';
		$method='/services/shipper/trackingEvents';
		$headers = $this->build_headers('POST', $server.$method);
		$body='["'.$wldh.'"]';
	
		$response = $this->send_request($method,$headers,$body);
		
		$response_arr = json_decode($response ,true);
		$order_row = Db::table('fa_order')->where('id',$ids)->find();
		$wl_row = Db::table('fa_wuliu')->where('order_id',$ids)->find();
		$wlid = $wl_row ? $wl_row['id'] : 0;
		if($response_arr['status'] == 'Success'){
			if($wl_row){
				$status = $wl_row['status'];
				$country_row = Db::table('fa_country')->where('ename',strtoupper($order_row['country']))->find();
				$siteconfig = Db::table('fa_config')->where('name','rkdays')->find();
				if(time() - $order_row['fhtime'] > $siteconfig['value'] * 86400 && $status == 0){
					$status = 3;
				}
				$fh = 0;
				foreach($response_arr['data'][0]['events'] as $k => $v){
					if($v['activity'] == 'RECEIVED SHIPMENT' && $response_arr['data'][0]['parcelStatus'] != 'Delivered'){
						$fh = 1;
						break;
					}
				}
				if($fh == 1){
					$status = 1;
				}elseif($response_arr['data'][0]['parcelStatus'] == 'Delivered'){
					$status = 2;
				}else{
					$deliver_days = isset($country_row) && $country_row['deliver_days'] != 0 ? $country_row['deliver_days'] * 1 : 0;
					if($deliver_days){
						if(time() - $order_row['fhtime'] > $deliver_days * 86400){
							$status = 4;
						}
						
					}
				}
				$rusult = Db::table('fa_wuliu')->where('id',$wl_row['id'])->update([
					'wltxt' => $response_arr['data'][0]['parcelStatus'],
					'datajson' => $response,
					'status' => $status,
				]);
				$wlid = $wl_row['id'];
			}else{
				Db::table('fa_wuliu')->insert([
					'admin_id' => $this->auth->id,
					'order_id' => $ids,
					'store_id' => $order_row['store_id'],
					'wltxt' => $response_arr['data'][0]['parcelStatus'],
					'datajson' => $response,
					'status' => $response_arr['data'][0]['events'][0]['activity'] == 'RECEIVED SHIPMENT' ? 1 : 0,
					'createtime' => time(),
					'updatetime' => time(),
				]);
				$wlid = Db::table('fa_wuliu')->getLastInsID();
			}
			if($response_arr['data'][0]['parcelStatus'] == 'Delivered'){
				Db::table('fa_order')->where('id',$ids)->update(['status' => 6]);
				Db::table('fa_ordergoods')->where('order_id',$ids)->update(['status' => 6]);
			}
		}else{
			if(!$wl_row){
				Db::table('fa_wuliu')->insert([
					'admin_id' => $this->auth->id,
					'order_id' => $ids,
					'status' => 0,
					'createtime' => time(),
					'updatetime' => time(),
				]);
				$wlid = Db::table('fa_wuliu')->getLastInsID();
			}else{
				$siteconfig = Db::table('fa_config')->where('name','rkdays')->find();
				if(time() - $order_row['fhtime'] > $siteconfig['value'] * 86400){
					Db::table('fa_wuliu')->where('id',$wl_row['id'])->update([
						'status' => 3,
					]);
				}
			}
		}
		if($order_row['wuliu_id'] == false){
			Db::table('fa_order')->where('id',$ids)->update(['wuliu_id'=>$wlid]);
		}
	}
	
	public function build_headers($method, $path, $acceptType='application/json'){
			
		$token = 'pclABrff5SvXocjSe01dPd';
		$key = 'XYss_bBkcLv-SGppb1MO_Q';
	
		$walltech_date=date(DATE_RFC7231,time()-60*60*8);
		$auth = $method."\n".$walltech_date."\n".$path;
		$hash=base64_encode(hash_hmac('sha1', $auth, $key, true));
		 // print_r($hash);exit;
		// echo $walltech_date."<br>".$auth."<br>".$hash."<br>";exit;
		return array(   'Content-Type: application/json',
						'Accept: '.$acceptType,
						'X-WallTech-Date: '.$walltech_date,
						'Authorization: WallTech '.$token.':'.$hash);
	}
	     
	 public  function send_request($method,$headers,$body){
		$server = 'http://cn.etowertech.com';
		$ch = curl_init($server.$method);
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST'); 
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		 
		curl_setopt($ch, CURLOPT_HTTPHEADER,$headers);  
		curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
		return curl_exec($ch);
	}
	
	
	public function peichang($ids = null)
	{
		$result = $this->model->where('id',$ids)->update(['status'=>2]);
		if (false === $result) {
		    $this->error(__('No rows were updated'));
		}
		$this->success();
	}
	
	

}
