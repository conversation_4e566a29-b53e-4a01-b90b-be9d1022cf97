<?php
/*
*Created By PhpStorm
*File:StoreMoneyLog.php
*User:尊杨科技
*Date:2025/5/11
*/

namespace app\admin\model;
use think\Model;


class StoreMoneyLog extends Model
{
    protected $name= 'store_money_log';
    protected $autoWriteTimestamp = 'integer';
    protected $createTime = 'add_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];

    // 状态列表
    public function getStatusList()
    {
        return ['0' => __('未打款'), '1' => __('已打款')];
    }

    // 状态获取器
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function store(){
        return $this->belongsTo('Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}