<?php
/*
*Created By PhpStorm
*File:Translate.php
*User:尊杨科技
*Date:2025/5/9
*/

namespace translate;

use think\Log;

class Translate
{
    private $key = '';
    private $url = '';

    public function __construct()
    {
        $this->key = "9089e42be3601677";
        $this->url = "https://v2.xxapi.cn/api/AIAutoTranslate";
    }

    public function translate($text, $from = 'auto', $to = 'zh')
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->url . '?text=' . $text . '&from=' . $from . '&to=' . $to . '&key=' . $this->key,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        Log::write($response);
        $response= json_decode($response,true);

        if($response['code']==200){
            return $response['data'];
        }else{
            return $response['msg'];
        }

    }
}