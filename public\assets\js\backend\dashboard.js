define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            require.config({
                paths: {
                    'Gzyechart': '../addons/echarts/js/gzyechart',
                }
            });
            var Echart = Config.erchart;
            require(['Gzyechart'], function () {
                Form.api.bindevent($("form[role=form]"));
                Gzyechart.echart.loadEchart('one', Echart.one.date, Echart.one.data);
                Gzyechart.echart.initButton();

                $(document).on("change", "#c-md_id", function () {
                    var md_id = $('#c-md_id').val();
                    var datetimerange = $(this).parent().parent().find(".datetimerange");
                    var url = datetimerange.data('url');
                    if (url.indexOf("md_id") != -1) {
                        url = Controller.api.replaceParamVal(url, "md_id", md_id);
                    } else {
                        url = url + "&md_id=" + md_id;
                    }
                    datetimerange.data('url', url);
					console.log(url);
                    var start, end;
                    var date = datetimerange.val();
                    if (date && date.length > 0) {
                        var datetime = date.split(' - ');
                        start = datetime[0] || '';
                        end = datetime[1] || '';
                    }
                    Gzyechart.echart.refreshEchart(datetimerange.data('type'), url, start, end);
                });
            });

			// 初始化表格参数配置
			Table.api.init({
			    showFooter: true,
			    extend: {
			        index_url: 'dashboard/index' + location.search,
			        table: 'dashboard',
			    }
			});

			// 确保排序功能启用
			$.extend($.fn.bootstrapTable.defaults, {
			    sortable: true
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
			    url: $.fn.bootstrapTable.defaults.extend.index_url,
			    pk: 'id',
			    sortName: 'id',
			    sortOrder: 'desc',
				search: false,
				showToggle: false,
				showColumns: false,
				searchFormVisible: true,
				// commonSearch: false,
				showExport: false,
				visible: false,
				// 自定义排序参数处理
				queryParams: function(params) {
				    // 检查是否是动态字段
				    if(['todaynum', 'weeknum', 'monthnum', 'num', 'diynum'].indexOf(params.sort) > -1) {
				        // 将排序字段保存在自定义参数中
				        params.custom_sort = params.sort;
				        params.custom_order = params.order;
				        // 使用id作为数据库排序字段
				        params.sort = 'id';
				    }
				    return params;
				},
			    columns: [
			        [
			            // {checkbox: true},
						// {field: 'id', title: __('Id')},
			            {
			                field: 'name', title: __('产品'), operate: false, sortable: true
			            },
						{
						    field: 'todaynum', title: __('日销量'), operate: false, sortable: true
						},
						{
						    field: 'weeknum', title: __('周销量'), operate: false, sortable: true
						},
						{
						    field: 'monthnum', title: __('月销量'), operate: false, sortable: true
						},
			            {
			                field: 'num', title: __('销售总量'), operate: false, sortable: true
			            },
						{
						    field: 'diynum', title: __('自定义时间销量'), operate: false, sortable: true
						},
			            {
			                field: 'ordertime', title: __('时间'), operate:'RANGE',
			                addclass:'datetimerange', autocomplete:false, visible: false,
			                formatter: Table.api.formatter.datetime, sortable: true
			            },
			        ]
			    ]
			});

			// 为表格绑定事件
			Table.api.bindevent(table);


        },
		api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            replaceParamVal: function (oUrl, paramName, replaceWith) {
                //替换指定传入参数的值,paramName为参数,replaceWith为新值
                var re = eval('/(' + paramName + '=)([^&]*)/gi');
                return oUrl.replace(re, paramName + '=' + replaceWith);
            },
        }
    };
    return Controller;
});
