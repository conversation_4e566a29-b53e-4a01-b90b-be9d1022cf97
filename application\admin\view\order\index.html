<style>
    /* 全局样式，确保工具栏始终可见 */
    body {
        overflow-x: hidden;
    }

    /* 确保工具栏始终可见 */
    #fixed-toolbar {
        background-color: #fff !important;
        padding: 10px !important;
        border-bottom: 1px solid #ddd !important;
        margin-bottom: 15px !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 9999 !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* 订单列表样式 */
    .order-item {
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #fff;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .order-item:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .order-item.selected {
        border: 2px solid #337ab7;
        box-shadow: 0 2px 12px rgba(51, 122, 183, 0.2);
    }

    .order-header {
        display: flex;
        background-color: #000;
        color: #fff;
        padding: 8px 0;
    }

    .order-header-item {
        flex: 1;
        padding: 0 10px;
        font-size: 14px;
        line-height: 20px;
    }

    .order-body {
        padding: 15px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .order-product {
        display: flex;
        /*width: 100%;*/
    }

    .product-image {
        max-width: 200px;
        max-height: 200px;
        width: auto;
        height: auto;
        object-fit: contain;
        border: 1px solid #eee;
        margin-right: 15px;
    }

    .product-info {
        /*flex: 1;*/
        width:550px;
    }

    .product-info-row {
        display: flex;
        margin-bottom: 8px;
    }

    .product-info-label {
        width: 80px;
        font-weight: bold;
        color: #333;
    }

    .product-info-value {
        flex: 1;
    }

    .product-customize-btn, .product-copy-btn {
        display: inline-block;
        padding: 2px 8px;
        background-color: #000;
        color: #fff;
        border-radius: 3px;
        margin-left: 5px;
        font-size: 12px;
        cursor: pointer;
    }

    .product-copy-btn {
        background-color: #1890ff;
    }

    .product-copy-btn:hover {
        background-color: #40a9ff;
    }

    .order-footer {
        padding: 10px 15px;
        background-color: #f9f9f9;
        border-top: 1px solid #eee;
    }

    .order-status {
        padding: 10px 15px;
        background-color: #f9f9f9;
        border-top: 1px solid #eee;
        color: #e74c3c;
    }

    .order-status.completed {
        color: #27ae60;
    }

    .order-status-label {
        font-weight: bold;
        margin-right: 10px;
    }

    .order-tracking {
        margin-top: 5px;
    }

    .order-tracking-number {
        font-family: monospace;
        color: #666;
    }

    .order-actions {
        margin-top: 10px;
        text-align: right;
    }

    .order-actions .btn {
        margin-left: 5px;
    }

    .view-toggle {
        margin-bottom: 10px;
    }

    .customer-info {
        flex: 1;
        padding: 0 15px;
    }

    .customer-info-title {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .customer-info-content {
        color: #666;
        line-height: 1.5;
    }

    .print-status {
        display: inline-block;
        padding: 2px 8px;
        background-color: #e74c3c;
        color: #fff;
        border-radius: 3px;
        margin-right: 5px;
        font-size: 12px;
    }

    .print-status.completed {
        background-color: #27ae60;
    }

    /* 申请单号按钮样式 */
    .apply-number-btn {
        background-color: #5bc0de !important;
        border-color: #46b8da !important;
        color: #fff !important;
        font-size: 12px !important;
        padding: 2px 8px !important;
        border-radius: 3px !important;
        text-decoration: none !important;
    }

    .apply-number-btn:hover {
        background-color: #31b0d5 !important;
        border-color: #269abc !important;
        color: #fff !important;
        text-decoration: none !important;
    }

    .apply-number-btn:disabled {
        background-color: #ccc !important;
        border-color: #ccc !important;
        cursor: not-allowed !important;
    }
</style>

<div class="panel panel-default panel-intro">

    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            <li class="{:$Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a>
            </li>
            {foreach name="statusList" item="vo"}
            <li class="{:$Think.get.status === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}"
                                                                                 data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>


    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <!-- 工具栏 - 使用固定定位确保始终显示 -->


                    <!-- 原始工具栏 - 隐藏但保留ID以兼容原有代码 -->
                    <div id="toolbar" class="toolbar" style="display: none;"></div>

                    <!-- 隐藏原始表格，但保留它用于获取数据 -->
                    <div style="display: none;">
                        <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                               data-operate-edit="{:$auth->check('order/edit')}"
                               data-operate-del="{:$auth->check('order/del')}"
                               width="100%">
                        </table>
                    </div>

                    <!-- 订单列表视图 -->
                    <div class="order-list-view">
                    <div class="clearfix" style="margin-bottom: 10px;" id="fixed-toolbar">
                        <div class="btn-group pull-left">
                            <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}"><i
                                    class="fa fa-refresh"></i> 刷新</a>
                            <a href="javascript:;"
                               class="btn btn-success btn-add {:$auth->check('order/add')?'':'hide'}"
                               title="{:__('Add')}"><i class="fa fa-plus"></i> {:__('Add')}</a>
                            <a href="javascript:;" class="btn btn-warning btn-dialog" data-url="order/import"
                               title="{:__('Import')}"><i class="fa fa-upload"></i> {:__('导入地址')}</a>
                            <a href="javascript:;" class="btn btn-danger btn-explode" title="{:__('订单导出')}"><i
                                    class="fa fa-download"></i> {:__('订单导出')}</a>
                        </div>
                        <div class="pull-right" style="width: 450px;">
                            <div class="input-group">
                                <input type="text" class="form-control search-input" placeholder="搜索订单...">
                                <div class="input-group-btn">
                                    <select class="form-control search-mode" style="width: auto; min-width: 100px;">
                                        <option value="1">全局模式</option>
                                        <option value="2">发货模式</option>
                                    </select>
                                </div>
                                <span class="input-group-btn">
                                    <button class="btn btn-default search-btn" type="button"><i
                                            class="fa fa-search"></i></button>
                                    <button class="btn btn-default clear-search-btn" type="button"><i
                                            class="fa fa-times"></i></button>
                                </span>
                            </div>
                        </div>
                    </div>
                        <div id="order-list-container">
                            <!-- 订单列表将通过JS动态生成 -->
                        </div>
                        <div class="text-center" style="margin-top: 10px;">
                            <ul id="order-list-pagination" class="pagination"></ul>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
