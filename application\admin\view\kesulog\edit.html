<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Kesu_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-kesu_id" data-rule="required" data-source="kesu/index" data-field="ksbh" class="form-control selectpage" name="row[kesu_id]" type="text" value="{$row.kesu_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gjtime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-gjtime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm" data-use-current="true" name="row[gjtime]" type="text" value="{:$row.gjtime?datetime($row.gjtime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gjtext')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-gjtext" data-rule="required" class="form-control" name="row[gjtext]" type="text" value="{$row.gjtext|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group" data-favisible="status=1">
        <label class="control-label col-xs-12 col-sm-2">{:__('Operate_type')}:</label>
        <div class="col-xs-12 col-sm-8">
    		<select  id="c-operate_type" data-rule="" class="form-control selectpicker" name="row[operate_type]">
    		    {foreach name="$site.ks_opetype" item="vo"}
    		        <option value="{$vo}" {in name="vo" value="$row.operate_type"}selected{/in}>{$vo}</option>
    		    {/foreach}
    		</select>
        </div>
    </div>
    <div class="form-group" data-favisible="status=1">
        <label class="control-label col-xs-12 col-sm-2">{:__('Operate_con')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-operate_con" class="form-control" name="row[operate_con]" type="text" value="{$row.operate_con|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
