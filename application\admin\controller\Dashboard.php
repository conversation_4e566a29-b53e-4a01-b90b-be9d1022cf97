<?php

namespace app\admin\controller;

use app\admin\model\Admin;
use app\admin\model\User;
use app\common\controller\Backend;
use app\common\model\Attachment;
use fast\Date;
use Exception;
use think\Db;

/**
 * 控制台
 *
 * @icon   fa fa-dashboard
 * @remark 用于展示当前系统中的统计数据、统计报表及重要实时数据
 */
class Dashboard extends Backend
{

	public function _initialize()
	{
	    parent::_initialize();
	    // $this->model = new \app\admin\model\Order;
	    $this->modelordergoods = new \app\admin\model\Ordergoods;
	    $this->model = new \app\admin\model\product\Sale;
	}

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
		if ($this->request->isAjax()) {
			if ($this->request->isAjax()) {
				if ($this->request->param('ajax') == 1) {
					return $this->getXstjEchartData();
				}
			}
			//如果发送的来源是Selectpage，则转发到Selectpage
			if ($this->request->request('keyField')) {
			    return $this->selectpage();
			}

			$filter = $this->request->get("filter", '');
			$filter = (array)json_decode($filter, true);
			$op = $this->request->get("op", '', 'trim');
			$op = (array)json_decode($op, true);
			$sj = array();
			if ( isset($filter['ordertime']) ) {
			    $ordertime = $filter['ordertime'];
			    list($startDate, $endDate) = explode(' - ', $ordertime);
			    !is_numeric($startDate) && $starttime = strtotime($startDate);
			    !is_numeric($endDate) && $endtime = strtotime($endDate);
			    $sj['ordertime'] = ['between time', [$starttime, $endtime]];
			    unset($filter['ordertime']);
			    unset($op['ordertime']);
			    $this->request->get(['filter' => json_encode($filter), 'op' => json_encode($op)]);
			}

			list($where, $sort, $order, $offset, $limit) = $this->buildparams();
			$list = $this->model
					->where($where)
					->order($sort, $order)
					->paginate($limit);
			$today = strtotime(date('Y-m-d',time()));
			$dayOfWeek = date('w', time());
			$mondayTimestamp = $today - ($dayOfWeek - 1) * 24 * 3600;
			$month = strtotime(date('Y-m',time()));

			$items = $list->items();
			foreach ($items as $row) {
				$row['diynum'] = $this->modelordergoods->where('product_sale_id',$row['id'])->where($sj)->count();
				$row['todaynum'] = $this->modelordergoods->where('product_sale_id',$row['id'])->where('ordertime','>',$today)->count();
				$row['weeknum'] = $this->modelordergoods->where('product_sale_id',$row['id'])->where('ordertime','>',$mondayTimestamp)->count();
				$row['monthnum'] = $this->modelordergoods->where('product_sale_id',$row['id'])->where('ordertime','>',$month)->count();
				$row['num'] = $this->modelordergoods->where('product_sale_id',$row['id'])->count();
			}

			// 处理前端传来的自定义排序参数
			$custom_sort = $this->request->param('custom_sort', '');
			$custom_order = $this->request->param('custom_order', '');

			if (!empty($custom_sort) && in_array($custom_sort, ['num', 'todaynum', 'weeknum', 'monthnum', 'diynum'])) {
			    // 对动态添加的字段进行排序
			    if ($custom_order == 'asc') {
			        usort($items, function($a, $b) use ($custom_sort) {
			            return $a[$custom_sort] - $b[$custom_sort];
			        });
			    } else {
			        usort($items, function($a, $b) use ($custom_sort) {
			            return $b[$custom_sort] - $a[$custom_sort];
			        });
			    }
			}
			// 如果没有自定义排序但是前端请求的是动态字段排序
			else if ($sort == 'num' || $sort == 'todaynum' || $sort == 'weeknum' || $sort == 'monthnum' || $sort == 'diynum') {
			    // 对动态添加的字段进行排序
			    if ($order == 'asc') {
			        usort($items, function($a, $b) use ($sort) {
			            return $a[$sort] - $b[$sort];
			        });
			    } else {
			        usort($items, function($a, $b) use ($sort) {
			            return $b[$sort] - $a[$sort];
			        });
			    }
			}

			$result = array("total" => $list->total(), "rows" => $items);
			return json($result);
		}

		$erchart = [
			'one' => $this->getXstjEchartData(false),
		];
		$this->assignconfig('erchart', $erchart);
		$this->view->assign('data', $erchart['one']['data']);
        return $this->view->fetch();
    }

    public function getXstjEchartData($is_ajax = true)
    {
        $startDate = $this->request->param('start_date', null);
        $endDate = $this->request->param('end_date', null);
        // $md_id = $this->request->param('md_id', null);

        // 生成查询的开始和结束时间，默认取30日
        !is_numeric($startDate) && $starttime = strtotime($startDate);
        !is_numeric($endDate) && $endtime = strtotime($endDate);
        $isnotrangeDate = empty($starttime) && empty($endtime);

        $nearly = '30';
        if ($isnotrangeDate) {
            $endtime = time();
            $nearly -= 1;
            $starttime = strtotime("-{$nearly} day");  // 最近30天日期
        } elseif ($starttime > $endtime) {
            $this->error = '起始时间要小于终止时间';
            return false;
        }
        $totalseconds = $endtime - $starttime;;
        if ($totalseconds > 86400 * 30 * 2) {
            $format = '%Y-%m';
        } else {
            if ($totalseconds > 86400) {
                $format = '%Y-%m-%d';
            } else {
                $format = '%H:00';
            }
        }

        if ($totalseconds > 84600 * 30 * 2) {
            $starttime = strtotime('last month', $starttime);
            while (($starttime = strtotime('next month', $starttime)) <= $endtime) {
                $column[] = date('Y-m', $starttime);
            }
        } else {
            if ($totalseconds > 86400) {
                for ($time = $starttime; $time <= $endtime;) {
                    $column[] = date("Y-m-d", $time);
                    $time += 86400;
                }
            } else {
                for ($time = $starttime; $time <= $endtime;) {
                    $column[] = date("H:00", $time);
                    $time += 3600;
                }
            }
        }

        $c_count = $d_count = array_fill_keys($column, 0);


		$field = 'DATE_FORMAT(FROM_UNIXTIME(ordertime), "' . $format . '") AS add_date';
		$where = array(
		    'ordertime' => ['between time', [$starttime, $endtime]]
		);
		$_where = [];
		if ( ! empty($md_id) ) {
		    $_where['id'] = ['in', $md_id];
		}


		// $list = $this->model->where($_where)->where($where)->select();


		$sj_data = [];
		$pro_rows = Db::table('fa_product_sale')->select();
		foreach($pro_rows as $k => $v){
			$name = $v['name'];
			$i = 0;
			foreach($c_count as $kk => $vv){
				$num = 0;
				$time_s = strtotime($kk);
				$time_o = strtotime($kk) + 60*60*24;
				$num = $this->modelordergoods
						->where('product_sale_id',$v['id'])
						->where('ordertime','>=',$time_s)
						->where('ordertime','<',$time_o)
						->count();
				$sj_data[$name][$i] = $num;
				$i ++;
			}
		}



		// print_r($sj_data);exit;


        $data = [
            'date' => array_keys($c_count),
            'data' => $sj_data,
        ];


        if ($is_ajax) {
            $this->success('', '', [
                'one' => $data,
                'count' => $c_count,
            ]);
        } else {
            return $data;
        }
    }

}
