define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'ordergoods/index' + location.search,
                    add_url: 'ordergoods/add',
                    edit_url: 'ordergoods/edit',
                    del_url: 'ordergoods/del',
                    multi_url: 'ordergoods/multi',
                    import_url: 'ordergoods/import',
                    table: 'ordergoods',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
				sortOrder: 'asc',
                fixedColumns: true,
                fixedRightNumber: 2,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
						
                        {field: 'ordertime', title: __('Ordertime'), operate:'RANGE', addclass:'datetimerange',datetimeFormat:"YYYY-MM-DD", autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'order.ddbh', title: __('Order.ddbh'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
						{field: 'xh', title: __('产品数量'), operate: false},
                        {field: 'store.name', title: __('Store.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'sale.name', title: __('Sale.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
						// {field: 'order.ifjjlist', title: __('是否加急'), searchList: {"0":__('正常'),"1":__('加急')}, formatter: Table.api.formatter.normal},
						{field: 'order.ifdblist', title: __('是否打包'), searchList: {"0":__('不打包'),"1":__('打包')}, formatter: Table.api.formatter.normal},
                        {field: 'sku_id', title: __('Sku_id'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.images},
                        {field: 'name', title: __('Name')},
                        {field: 'bmdk', title: __('Bmdk'),cellStyle: Controller.api.formatter.css},
                        {field: 'zfdy', title: __('Zfdy'),cellStyle: Controller.api.formatter.css},
						
                        {field: 'tsyqimages', title: __('Tsyqimages'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.images},
						
                        {field: 'tsyq', title: __('Tsyq'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'sfdy', title: __('Sfdy'), searchList: {"0":__('Sfdy 0'),"1":__('Sfdy 1')}, formatter: Table.api.formatter.normal},
                        {field: 'sfqg', title: __('Sfqg'), searchList: {"0":__('Sfqg 0'),"1":__('Sfqg 1')}, formatter: Table.api.formatter.normal},
						
						{field: 'status', title: __('状态'), searchList: {"0":__('Status 0'),"1":__('Status 1'),"2":__('Status 2'),"3":__('Status 3'),"4":__('Status 4'),"5":__('Status 5'),"6":__('Status 6')}, formatter: Table.api.formatter.status},
						 {
						    field: 'buttons',
						    width: "120px",
						    title: __('操作'),
						    table: table,
						    events: Table.api.events.operate,
						    buttons: [
								{
								    name: 'detail',
								    text: __('雕刻复制'),
								    title: __('雕刻复制'),
								    classname: 'btn btn-xs btn-primary btn-dialog',
								    icon: 'fa fa-list',
								    url: 'ordergoods/copy/field/bmdk',
								    callback: function (data) {
										Layer.msg('复制成功');
										return false;
								        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
								    },
								    visible: function (row) {
								        //返回true时按钮显示,返回false隐藏
								        return true;
								    }
								},
								{
								    name: 'detail',
								    text: __('祝福复制'),
								    title: __('祝福复制'),
								    classname: 'btn btn-xs btn-primary btn-dialog',
								    icon: 'fa fa-list',
								    url: 'ordergoods/copy/field/zfdy',
								    callback: function (data) {
										Layer.msg('复制成功');
										return false;
								        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
								    },
								    visible: function (row) {
								        //返回true时按钮显示,返回false隐藏
								        return true;
								    }
								},
								{
								    name: 'ajax',
								    text: __('打印'),
								    title: __('打印'),
								    classname: 'btn btn-xs btn-success btn-magic btn-ajax',
								    icon: 'fa fa-magic',
								    url: 'ordergoods/operate/type/1',
								    confirm: '是否已打印？',
									visible :function(row){
										if(row.sfdy == 0 && row.status == 0){
											return true;
										}else{
											return false;
										}
									},
								    success: function (data, ret) {
								        layer.msg(__('操作成功！'));
								        table.bootstrapTable('refresh', {});
								        return false;
								    },
								    error: function (data, ret) {
								        console.log(data, ret);
								        Layer.alert(ret.msg);
								        return false;
								    }
								},
						        {
						            name: 'ajax',
						            text: __('切割'),
						            title: __('切割'),
						            classname: 'btn btn-xs btn-success btn-magic btn-ajax',
						            icon: 'fa fa-magic',
						            url: 'ordergoods/operate/type/2',
						            confirm: '是否已切割？',
									visible :function(row){
										if(row.sfqg == 0 && row.status == 0){
											return true;
										}else{
											return false;
										}
									},
						            success: function (data, ret) {
						                layer.msg(__('操作成功！'));
						                table.bootstrapTable('refresh', {});
						                return false;
						            },
						            error: function (data, ret) {
						                console.log(data, ret);
						                Layer.alert(ret.msg);
						                return false;
						            }
						        },
								
								{
								    name: 'ajax',
								    text: __('生产'),
								    title: __('生产'),
								    classname: 'btn btn-xs btn-success btn-magic btn-ajax',
								    icon: 'fa fa-magic',
								    url: 'ordergoods/operate/type/3',
								    confirm: '是否已生产？',
									visible :function(row){
										if(row.sfdy == 1 && row.sfqg == 1 && row.status == 1){
											return true;
										}else{
											return false;
										}
									},
								    success: function (data, ret) {
								        layer.msg(__('操作成功！'));
								        table.bootstrapTable('refresh', {});
								        return false;
								    },
								    error: function (data, ret) {
								        console.log(data, ret);
								        Layer.alert(ret.msg);
								        return false;
								    }
								},
						        {
									name: 'detail',
									text: __('订单发货'),
									title: __('订单发货'),
									classname: 'btn btn-xs btn-danger btn-dialog',
									icon: 'fa fa-list',
									url: 'ordergoods/send',
									visible :function(row){
										if(row.iffh == 0 && row.status == 2){
											return true;
										}else{
											return false;
										}
									},
									callback: function (data) {
										Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
									},
								},
						    ],
						    formatter: Table.api.formatter.buttons
						}
                        
                        // {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
		send: function () {
		    Controller.api.bindevent();
		},
		copy: function () {
		    Controller.api.bindevent();
		},
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
			formatter:{
				css:function(){
					return {
						css:{
							"max-width":"300px",
							"min-width":"300px",
							"white-space":"unset",
							"height":"auto",
							"line-height":"22px"
						}
					}
				}
			}
        }
    };
    return Controller;
});

function copy() {
		var input = document.getElementById("fieldcon");
		// input.value = document.getElementById(id).innerText; // 修改文本框的内容
		input.select(); // 选中文本
	
		document.execCommand("copy"); // 执行浏览器复制命令
	
		layer.msg("复制成功！");
		Fast.api.close();
	}