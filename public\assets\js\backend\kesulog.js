define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'kesulog/index' + location.search,
                    add_url: 'kesulog/add',
                    edit_url: 'kesulog/edit',
                    del_url: 'kesulog/del',
                    multi_url: 'kesulog/multi',
                    import_url: 'kesulog/import',
                    table: 'kesulog',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'admin.nickname', title: __('跟进人'), operate: 'LIKE'},
                        {field: 'kesu.ksbh', title: __('Kesu.ksbh'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'order.ddbh', title: __('Order.ddbh'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'gjtime', title: __('Gjtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'gjtext', title: __('Gjtext'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1')}, formatter: Table.api.formatter.status},
                        {field: 'operate_type', title: __('Operate_type'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'operate_con', title: __('Operate_con'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
			Table.api.init();
			this.table.first();
			
        },
        edit: function () {
            Controller.api.bindevent();
        },
		table: {
		    first: function () {
				var table1 = $("#table1");
				table1.bootstrapTable({
				    url: 'kesulog/index',
				    extend: {
				        index_url: 'kesulog/index' + location.search,
				        add_url: 'kesulog/add',
				        edit_url: 'kesulog/edit',
				        del_url: 'kesulog/del',
				        multi_url: 'kesulog/multi',
				        import_url: 'kesulog/import',
				        table: 'kesulog',
				    },
				    pk: 'id',
				    sortName: 'id',
				    fixedColumns: true,
				    fixedRightNumber: 1,
					queryParams: function (params) {
					    //这里可以追加搜索条件
					    var filter = JSON.parse(params.filter);
					    var op = JSON.parse(params.op);
					    //这里可以动态赋值，比如从URL中获取admin_id的值，filter.admin_id=Fast.api.query('admin_id');
						filter.kesu_id=Fast.api.query('kesu_id');
					    op.kesu_id = "=";
					    params.filter = JSON.stringify(filter);
					    params.op = JSON.stringify(op);
					    return params;
					},
				    columns: [
				        [
				            {checkbox: true},
				            {field: 'id', title: __('Id')},
				            {field: 'admin.nickname', title: __('跟进人'), operate: 'LIKE'},
				            {field: 'kesu.ksbh', title: __('Kesu.ksbh'), operate: 'LIKE', table: table1, class: 'autocontent', formatter: Table.api.formatter.content},
				            {field: 'order.ddbh', title: __('Order.ddbh'), operate: 'LIKE', table: table1, class: 'autocontent', formatter: Table.api.formatter.content},
				            {field: 'gjtime', title: __('Gjtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
				            {field: 'gjtext', title: __('Gjtext'), operate: 'LIKE', table: table1, class: 'autocontent', formatter: Table.api.formatter.content},
				            {field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1')}, formatter: Table.api.formatter.status},
				            {field: 'operate_type', title: __('Operate_type'), operate: 'LIKE', table: table1, class: 'autocontent', formatter: Table.api.formatter.content},
				            {field: 'operate_con', title: __('Operate_con'), operate: 'LIKE', table: table1, class: 'autocontent', formatter: Table.api.formatter.content},
				            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
				            
				            {field: 'operate', title: __('Operate'), table: table1, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
				        ]
				    ]
				});
				// 为表格1绑定事件
				Table.api.bindevent(table1);
			}
		},
		
		
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
