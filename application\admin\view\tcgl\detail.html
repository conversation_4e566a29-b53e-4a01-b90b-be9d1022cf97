<form id="send-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	
	<div class="orderbox">
		<h3 class="center topname">头程详情</h3>
		<div class="conbox">
			<div class="tit">
				<span>物流单号：{:$row.wldh}</span>
				<span>发货日期：{:$row.fhtime ? date('Y-m-d',$row.fhtime) : '无'}</span>
				<span>物流商：{:$row.wls}</span>
			</div>
			
			{if count($order)}
			<div class="detail m60">
				<table class="table table-striped table-bordered table-hover table-nowrap">
					<tr>
						<td>订单号</td>
						<td>物流单号</td>
					</tr>
					{foreach name="$order" item="vo"}
					<tr>
						<td>{$vo.ddbh}</td>
						<td>{$vo.wldh}</td>
					</tr>
					{/foreach}
				</table>
				
			</div>
			{/if}
			
		</div>
	</div>
	
    
    
</form>
<script>
	function copyText(text) {
		var input = document.getElementById("input");
		input.value = text; // 修改文本框的内容

		input.select(); // 选中文本

		document.execCommand("copy"); // 执行浏览器复制命令

		layer.msg("复制成功！");
	}
	
	function fangda(img){
		layer.open({
			type: 1,
			title: '图片',
			fix: true,
			shadeClose: true,
			shade: 0,
			area: ['500px', '500px'],
			content: "<img class='fdimg' src='"+img+"'>"
		});
	}
</script>

<style>
	.flex{display: flex;}
	.center{text-align: center;}
	.m30{margin:30px 0;}
	.m60{margin:60px 0;}
	.ml10{margin-left: 10px;}
	.b{font-weight: 900;}
	.conlist{justify-content: space-between;}
	.item-center{align-items: center;}
	.itembox{margin-right: 50px;}
	.itembox .item{padding: 20px 0;}
	.itembox .img img{margin-right: 50px;width:140px}
	.topname{margin: 30px auto;font-family: 900;font-size: 40px;}
	.itembox .text p{margin-bottom: 5px;}
	.conbox{padding:20px 50px;}
	.conbox .tit span{margin-right: 20px;padding: 10px 20px;background-color: #000;color: #fff;}
	.btn{padding: 3px 10px;color: #fff;background-color: #000;margin-right: 10px;}
	.btn:hover{color:#fff;background-color: #666;}
	.btn.red{background-color: #f00;}
	.status span{display: block;margin: 20px 0;}
	.conlist{border:1px solid #ccc;padding:0 20px;}
	.fot{border:1px solid #f00;padding: 20px;}
	.select{width: 30px;margin-right: 30px;display: none;}
	#input{position: absolute;top: 0;left: 0;opacity: 0;z-index: -10;}
	.imgicon{width:35px;height:35px;border: 1px solid #eee;border-radius: 3px;}
	.pointer{cursor: pointer;}
	.fdimg{height:calc(100% - 20px);width:auto;display: block;margin: 10px auto;max-width: 100%;max-height: calc(100% - 20px);}
	
	h2{text-align: center;margin-bottom: 20px;font-size: 36px;line-height: 50px;}
	.detail table td{padding: 10px !important;}
</style>
