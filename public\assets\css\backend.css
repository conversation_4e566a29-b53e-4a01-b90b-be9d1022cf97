@import url("../css/bootstrap.css");
@import url("../css/fastadmin.css");
@import url("../css/skins/skin-black-blue.css");
@import url("../css/iconfont.css");
@import url("../libs/font-awesome/css/font-awesome.min.css");
@import url("../libs/toastr/toastr.min.css");
@import url("../libs/fastadmin-layer/dist/theme/default/layer.css");
@import url("../libs/bootstrap-table/dist/bootstrap-table.min.css");
@import url("../libs/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css");
@import url("../libs/bootstrap-daterangepicker/daterangepicker.css");
@import url("../libs/nice-validator/dist/jquery.validator.css");
@import url("../libs/bootstrap-select/dist/css/bootstrap-select.min.css");
@import url("../libs/fastadmin-selectpage/selectpage.css");
@import url("../libs/bootstrap-slider/slider.css");
.m-0 {
  margin-top: 0px !important;
  margin-right: 0px !important;
  margin-bottom: 0px !important;
  margin-left: 0px !important;
}
.mt-0 {
  margin-top: 0px !important;
}
.mr-0 {
  margin-right: 0px !important;
}
.mb-0 {
  margin-bottom: 0px !important;
}
.ml-0 {
  margin-left: 0px !important;
}
.mx-0 {
  margin-left: 0px !important;
  margin-right: 0px !important;
}
.my-0 {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}
.m-1 {
  margin-top: 5px !important;
  margin-right: 5px !important;
  margin-bottom: 5px !important;
  margin-left: 5px !important;
}
.mt-1 {
  margin-top: 5px !important;
}
.mr-1 {
  margin-right: 5px !important;
}
.mb-1 {
  margin-bottom: 5px !important;
}
.ml-1 {
  margin-left: 5px !important;
}
.mx-1 {
  margin-left: 5px !important;
  margin-right: 5px !important;
}
.my-1 {
  margin-top: 5px !important;
  margin-bottom: 5px !important;
}
.m-2 {
  margin-top: 10px !important;
  margin-right: 10px !important;
  margin-bottom: 10px !important;
  margin-left: 10px !important;
}
.mt-2 {
  margin-top: 10px !important;
}
.mr-2 {
  margin-right: 10px !important;
}
.mb-2 {
  margin-bottom: 10px !important;
}
.ml-2 {
  margin-left: 10px !important;
}
.mx-2 {
  margin-left: 10px !important;
  margin-right: 10px !important;
}
.my-2 {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}
.m-3 {
  margin-top: 15px !important;
  margin-right: 15px !important;
  margin-bottom: 15px !important;
  margin-left: 15px !important;
}
.mt-3 {
  margin-top: 15px !important;
}
.mr-3 {
  margin-right: 15px !important;
}
.mb-3 {
  margin-bottom: 15px !important;
}
.ml-3 {
  margin-left: 15px !important;
}
.mx-3 {
  margin-left: 15px !important;
  margin-right: 15px !important;
}
.my-3 {
  margin-top: 15px !important;
  margin-bottom: 15px !important;
}
.m-4 {
  margin-top: 20px !important;
  margin-right: 20px !important;
  margin-bottom: 20px !important;
  margin-left: 20px !important;
}
.mt-4 {
  margin-top: 20px !important;
}
.mr-4 {
  margin-right: 20px !important;
}
.mb-4 {
  margin-bottom: 20px !important;
}
.ml-4 {
  margin-left: 20px !important;
}
.mx-4 {
  margin-left: 20px !important;
  margin-right: 20px !important;
}
.my-4 {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}
.p-0 {
  padding-top: 0px !important;
  padding-right: 0px !important;
  padding-bottom: 0px !important;
  padding-left: 0px !important;
}
.pt-0 {
  padding-top: 0px !important;
}
.pr-0 {
  padding-right: 0px !important;
}
.pb-0 {
  padding-bottom: 0px !important;
}
.pl-0 {
  padding-left: 0px !important;
}
.px-0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}
.py-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.p-1 {
  padding-top: 5px !important;
  padding-right: 5px !important;
  padding-bottom: 5px !important;
  padding-left: 5px !important;
}
.pt-1 {
  padding-top: 5px !important;
}
.pr-1 {
  padding-right: 5px !important;
}
.pb-1 {
  padding-bottom: 5px !important;
}
.pl-1 {
  padding-left: 5px !important;
}
.px-1 {
  padding-left: 5px !important;
  padding-right: 5px !important;
}
.py-1 {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}
.p-2 {
  padding-top: 10px !important;
  padding-right: 10px !important;
  padding-bottom: 10px !important;
  padding-left: 10px !important;
}
.pt-2 {
  padding-top: 10px !important;
}
.pr-2 {
  padding-right: 10px !important;
}
.pb-2 {
  padding-bottom: 10px !important;
}
.pl-2 {
  padding-left: 10px !important;
}
.px-2 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}
.py-2 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}
.p-3 {
  padding-top: 15px !important;
  padding-right: 15px !important;
  padding-bottom: 15px !important;
  padding-left: 15px !important;
}
.pt-3 {
  padding-top: 15px !important;
}
.pr-3 {
  padding-right: 15px !important;
}
.pb-3 {
  padding-bottom: 15px !important;
}
.pl-3 {
  padding-left: 15px !important;
}
.px-3 {
  padding-left: 15px !important;
  padding-right: 15px !important;
}
.py-3 {
  padding-top: 15px !important;
  padding-bottom: 15px !important;
}
.p-4 {
  padding-top: 20px !important;
  padding-right: 20px !important;
  padding-bottom: 20px !important;
  padding-left: 20px !important;
}
.pt-4 {
  padding-top: 20px !important;
}
.pr-4 {
  padding-right: 20px !important;
}
.pb-4 {
  padding-bottom: 20px !important;
}
.pl-4 {
  padding-left: 20px !important;
}
.px-4 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}
.py-4 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}
html,
body {
  height: 100%;
}
body {
  background: #f1f4f6;
  font-size: 14px;
  line-height: 1.5715;
}
body.is-dialog {
  background: #fff;
}
.dropdown-menu > li > a {
  padding: 5px 12px;
}
.selection {
  position: absolute;
  border: 1px solid #8B9;
  background-color: #BEC;
}
.main-header .navbar {
  position: relative;
}
.main-header .navbar .dropdown-menu {
  font-size: 14px;
}
.main-header .navbar .dropdown-menu > li > a {
  padding: 8px 15px;
}
.bootstrap-dialog .modal-dialog {
  /*width: 70%;*/
  max-width: 885px;
}
/*iOS兼容*/
html.ios-fix,
html.ios-fix body {
  height: 100%;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}
html.ios-fix .wrapper,
html.ios-fix body .wrapper,
html.ios-fix .tab-pane,
html.ios-fix body .tab-pane {
  -webkit-overflow-scrolling: touch;
}
.wrapper {
  height: 100%;
}
.content-wrapper {
  position: relative;
  height: 100%;
}
.control-relative {
  position: relative;
}
.tab-addtabs .tab-pane {
  height: 100%;
  width: 100%;
}
.row-between .col-xs-6 + .col-xs-6:before {
  content: "-";
  position: absolute;
  left: -2%;
  top: 6px;
}
@media only screen and (min-width: 481px) {
  .row-flex {
    display: flex;
    flex-wrap: wrap;
  }
  .row-flex > [class*='col-'] {
    display: flex;
    flex-direction: column;
  }
  .row-flex.row:after,
  .row-flex.row:before {
    display: flex;
  }
}
@media (max-width: 991px) {
  .main-header .navbar-custom-menu a.btn-danger {
    color: #fff;
    background-color: #f75444;
  }
  .main-header .navbar-custom-menu a.btn-primary {
    color: #fff;
    background-color: #444c69;
  }
}
.common-search-table {
  min-height: 20px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #f5f5f5;
}
/* 固定的底部按钮 */
.fixed-footer {
  position: fixed;
  bottom: 0;
  background-color: #ecf0f1;
  width: 100%;
  margin-bottom: 0;
  padding: 10px;
}
table.table-template {
  overflow: hidden;
}
.sp_container .msg-box {
  position: absolute;
  right: 0;
  top: 0;
}
.sp_container .sp_element_box {
  overflow: unset;
}
.sp_container .sp_element_box > li.input_box {
  position: unset;
}
.sp_container .sp_element_box .msg-box {
  right: -24px;
}
@media (max-width: 767px) {
  .sp_container .sp_element_box .msg-box {
    left: inherit;
  }
  .card-views .card-view {
    padding: 5px 0;
  }
}
.toast-top-right-index {
  top: 62px;
  right: 12px;
}
.bootstrap-select {
  min-height: 33px;
}
.bootstrap-select .msg-box {
  position: absolute;
  right: 0;
  top: 0;
}
.bootstrap-select .status {
  background: #f0f0f0;
  clear: both;
  color: #999;
  font-size: 13px;
  font-weight: 500;
  line-height: 1;
  margin-bottom: -5px;
  padding: 10px 20px;
}
.bootstrap-select .bs-placeholder {
  min-height: 33px;
}
select.bs-select-hidden,
select.selectpicker {
  display: inherit !important;
  max-height: 33px;
  overflow: hidden;
}
select.bs-select-hidden[multiple],
select.selectpicker[multiple] {
  height: 33px;
  padding: 0;
  background: #f4f4f4;
}
select.bs-select-hidden[multiple] option,
select.selectpicker[multiple] option {
  color: #f4f4f4;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
@media not all and (min-resolution: 0.001dpcm) {
  @supports (-webkit-appearance:none) {
    select.bs-select-hidden[multiple],
    select.selectpicker[multiple] {
      visibility: hidden;
    }
  }
}
input.selectpage {
  color: transparent;
  pointer-events: none;
}
.sp_container {
  min-height: 33px;
}
.sp_container input.selectpage {
  color: inherit;
  pointer-events: inherit;
  padding-left: 12px;
  padding-right: 12px;
}
.sp_container .sp_element_box input.selectpage {
  padding-left: 0;
  padding-right: 0;
}
.sp_container .sp_element_box li:first-child input.selectpage {
  padding-left: 9px;
  padding-right: 9px;
}
.img-center {
  margin: 0 auto;
  display: inline;
  float: none;
}
/*
 * RIBBON
 */
#ribbon {
  overflow: hidden;
  padding: 15px 15px 0 15px;
  position: relative;
}
#ribbon a {
  color: #777 !important;
  text-decoration: none !important;
}
#ribbon .breadcrumb {
  display: inline-block;
  margin: 0;
  padding: 0;
  background: none;
  vertical-align: top;
}
#ribbon .breadcrumb > .active,
#ribbon .breadcrumb li {
  color: #aaa;
}
#ribbon .shortcut a {
  margin-left: 10px;
}
.is-dialog #main {
  background: #fff;
}
.is-dialog .layer-footer {
  display: none;
}
form.form-horizontal .control-label {
  font-weight: normal;
}
.user-panel > .image img {
  width: 45px;
  height: 45px;
}
/*panel扩展描述样式*/
.panel-intro {
  margin-bottom: 0;
  border: none;
}
.panel-intro > .panel-heading {
  padding: 15px;
  padding-bottom: 0;
  background: #e8edf0;
  border-color: #e8edf0;
  position: relative;
}
.panel-intro > .panel-heading .panel-lead {
  margin-bottom: 15px;
}
.panel-intro > .panel-heading .panel-lead em {
  display: block;
  font-weight: bold;
  font-style: normal;
}
.panel-intro > .panel-heading .panel-title {
  height: 25px;
  font-weight: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.panel-intro > .panel-heading .panel-control {
  height: 42px;
  position: absolute;
  top: 8px;
  right: 8px;
}
.panel-intro > .panel-heading .panel-control .fa {
  font-size: 14px;
}
.panel-intro > .panel-heading .nav-tabs {
  border-bottom: 0;
  margin-bottom: 0;
}
.panel-intro > .panel-heading .nav-tabs > li > a {
  margin-right: 4px;
  color: #95a5a6;
  background-color: #d8e0e6;
  border: 1px solid #e8edf0;
  border-bottom-color: transparent;
}
.panel-intro > .panel-heading .nav-tabs > li > a:hover,
.panel-intro > .panel-heading .nav-tabs > li > a:focus {
  border: 1px solid #e8edf0;
  color: #7b8a8b;
  background-color: #c9d4dc;
}
.panel-intro > .panel-heading .nav-tabs > li.active > a,
.panel-intro > .panel-heading .nav-tabs > li.active > a:hover,
.panel-intro > .panel-heading .nav-tabs > li.active > a:focus {
  color: #7b8a8b;
  background-color: #ffffff;
  border-bottom-color: transparent;
  cursor: default;
}
@media (max-width: 768px) {
  .panel-intro > .panel-heading .nav-tabs {
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    margin-bottom: -1px;
  }
  .panel-intro > .panel-heading .nav-tabs > li {
    display: inline-block;
    float: none;
  }
}
/*单表格*/
.panel-tabs .panel-heading {
  padding: 12px 15px 12px 15px;
}
.panel-tabs .panel-heading .panel-lead {
  margin-bottom: 0px;
}
/*选项卡*/
.panel-nav .panel-heading {
  padding: 0px;
  padding-bottom: 0;
  background: #f1f4f6;
  border-color: #f1f4f6;
}
.panel-nav .nav-tabs > li > a {
  padding: 12px 15px;
  background-color: #e8edf0;
  border: 1px solid #f1f4f6;
}
.panel-nav .nav-tabs > li > a:hover,
.panel-nav .nav-tabs > li > a:focus {
  border: 1px solid #e8edf0;
  background-color: #e8edf0;
}
.panel-nav .nav-tabs > li.active > a,
.panel-nav .nav-tabs > li.active > a:hover,
.panel-nav .nav-tabs > li.active > a:focus {
  border-color: #f1f4f6;
  border-bottom-color: transparent;
}
/*顶栏addtabs*/
.nav-addtabs {
  height: 100%;
  border: none;
}
.nav-addtabs.disable-top-badge > li > a > .pull-right-container {
  display: none;
}
.nav-addtabs > li {
  margin: 0;
}
.nav-addtabs > li > a {
  height: 50px;
  line-height: 50px;
  padding: 0 15px;
  border-radius: 0;
  border: none;
  border-right: 1px solid rgba(0, 0, 0, 0.05);
  margin: 0;
  color: #95a5a6;
}
.nav-addtabs > li > a:hover,
.nav-addtabs > li > a:focus {
  border: none;
  color: #2c3e50;
  border-right: 1px solid rgba(0, 0, 0, 0.02);
}
.nav-addtabs > li.active > a {
  height: 50px;
  line-height: 50px;
  padding: 0 15px;
  border-radius: 0;
  border: none;
  border-right: 1px solid rgba(0, 0, 0, 0.02);
  background: #f1f4f6;
  color: #2c3e50;
  overflow: hidden;
}
.nav-addtabs > li.active > a:hover,
.nav-addtabs > li.active > a:focus {
  border: none;
  color: #2c3e50;
  background: #f1f4f6;
  border-right: 1px solid rgba(0, 0, 0, 0.02);
}
.nav-addtabs > li .close-tab {
  font-size: 10px;
  position: absolute;
  right: 0px;
  top: 50%;
  margin-top: -8px;
  z-index: 100;
  cursor: pointer;
  color: #eee;
  display: none;
}
.nav-addtabs > li .close-tab:before {
  content: "\e626";
  font-family: iconfont;
  font-style: normal;
  font-weight: normal;
  text-decoration: inherit;
  font-size: 18px;
}
.nav-addtabs .open > a:hover,
.nav-addtabs .open > a:focus {
  border-right: 1px solid rgba(0, 0, 0, 0.05);
}
.nav-addtabs ul li {
  position: relative;
}
.nav-addtabs li:hover > .close-tab {
  display: block;
}
#firstnav {
  height: 50px;
  border-bottom: 1px solid transparent;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: relative;
}
#firstnav .sidebar-toggle {
  position: absolute;
  width: 45px;
  text-align: center;
  height: 50px;
  line-height: 50px;
  padding: 0;
}
#firstnav .nav-addtabs {
  position: absolute;
  left: 45px;
  z-index: 98;
}
#firstnav .navbar-custom-menu {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 99;
  background: transparent;
}
/*次栏菜单栏*/
#secondnav {
  display: none;
  height: 44px;
  position: absolute;
  top: 50px;
  left: 0;
  background: #fff;
  width: 100%;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 5px 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
#secondnav .nav-addtabs {
  height: 100%;
  border: none;
}
#secondnav .nav-addtabs.disable-top-badge > li > a > .pull-right-container {
  display: none;
}
#secondnav .nav-addtabs > li {
  border: 1px solid #eee;
  border-radius: 3px;
  padding: 0 15px;
  height: 30px;
  line-height: 30px;
  margin: 2px 5px 2px 0;
  background: #fff;
}
#secondnav .nav-addtabs > li > a {
  display: block;
  color: #495060 !important;
  height: 100%;
  padding: 0;
  line-height: 28px;
  font-size: 13px;
  vertical-align: middle;
  opacity: 1;
  overflow: hidden;
  background: none;
  border: none;
}
#secondnav .nav-addtabs > li.active {
  border-color: #bdbebd;
  background-color: #f7f7f7;
}
#secondnav .nav-addtabs > li .close-tab {
  font-size: 10px;
  position: absolute;
  right: 0px;
  top: 50%;
  margin-top: -8px;
  z-index: 100;
  cursor: pointer;
  color: #eee;
}
#secondnav .nav-addtabs > li .close-tab:before {
  content: "\e626";
  font-family: iconfont;
  font-style: normal;
  font-weight: normal;
  text-decoration: inherit;
  font-size: 18px;
}
#secondnav .nav-addtabs > li:hover,
#secondnav .nav-addtabs > li:focus {
  border-color: #bdbebd;
}
#secondnav .nav-addtabs ul li {
  position: relative;
}
#secondnav .nav-addtabs li:hover > .close-tab {
  display: block;
  border-color: #222e32;
  color: #222e32;
}
.multiplenav .content-wrapper,
.multiplenav .right-side,
.multiplenav .main-sidebar {
  padding-top: 50px;
}
.multiplenav #firstnav .nav-addtabs {
  padding-right: 450px;
}
@media (max-width: 767px) {
  .multipletab.multiplenav .content-wrapper,
  .multipletab.multiplenav .right-side {
    padding-top: 94px;
  }
}
.multipletab #secondnav {
  display: block;
}
.multipletab.multiplenav .content-wrapper,
.multipletab.multiplenav .right-side {
  padding-top: 94px;
}
.multipletab.multiplenav #firstnav .nav-tabs {
  overflow: hidden;
}
.main-sidebar .sidebar-form {
  overflow: visible;
}
.main-sidebar .sidebar-form .menuresult {
  z-index: 999;
  position: absolute;
  top: 34px;
  left: -1px;
  width: 100%;
  max-height: 250px;
  overflow: auto;
  margin: 0;
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.main-sidebar .sidebar-form .menuresult a {
  display: block;
  background-color: #fff;
  border-top: 1px solid transparent;
  border-bottom: 1px solid #eee;
  padding: 10px 15px;
  color: #222d32;
}
.main-sidebar .sidebar-form .menuresult a:hover {
  background: #eee;
}
.main-sidebar .sidebar-form .menuresult a:first-child {
  border-top: 1px solid #eee;
}
.input-group .sp_result_area {
  width: 100%;
}
.sidebar-menu .treeview-open > .treeview-menu {
  display: block;
}
.sidebar-menu > li .badge {
  margin-top: 0;
}
.sidebar-collapse .user-panel > .image img {
  width: 25px;
  height: 25px;
}
@media (min-width: 768px) {
  .sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > .pull-right-container {
    top: 7px !important;
    right: 10px;
    height: 17px;
  }
}
.fieldlist dd {
  display: block;
  margin: 8px 0;
}
.fieldlist dd input {
  display: inline-block;
  width: 300px;
}
.fieldlist dd input:first-child {
  width: 110px;
}
.fieldlist dd ins {
  width: 110px;
  display: inline-block;
  text-decoration: none;
}
/* 弹窗中的表单 */
.form-layer {
  height: 100%;
  min-height: 150px;
  min-width: 300px;
}
.form-layer .form-body {
  width: 100%;
  overflow: auto;
  top: 0;
  position: absolute;
  z-index: 10;
  bottom: 50px;
  padding: 15px;
}
.form-layer .form-footer {
  height: 50px;
  line-height: 50px;
  background-color: #ecf0f1;
  width: 100%;
  position: absolute;
  z-index: 200;
  bottom: 0;
  margin: 0;
}
.form-layer .form-footer .form-group {
  margin-left: 0;
  margin-right: 0;
}
#treeview .jstree-container-ul .jstree-node {
  display: block;
  clear: both;
}
#treeview .jstree-leaf:not(:first-child) {
  float: left;
  background: none;
  margin-left: 0;
  min-width: 80px;
  clear: none;
}
#treeview .jstree-leaf {
  float: left;
  margin-left: 0;
  padding-left: 24px;
  min-width: 80px;
  clear: none;
  color: #777;
}
#treeview .jstree-leaf > .jstree-icon,
#treeview .jstree-leaf .jstree-themeicon {
  display: none;
}
#treeview .jstree-last {
  background-image: url("../img/32px.png");
  background-position: -292px -4px;
  background-repeat: repeat-y;
}
#treeview .jstree-children:before,
#treeview .jstree-children:after {
  content: " ";
  display: table;
}
#treeview .jstree-children:after {
  clear: both;
}
#treeview .jstree-themeicon {
  display: none;
}
/*去除bootstrap-table的边框*/
.fixed-table-container {
  border: none !important;
}
.fixed-table-container tbody .selected td {
  background-color: rgba(216, 224, 230, 0.5);
}
.fixed-table-container .bs-checkbox {
  min-width: 36px;
}
.fixed-table-container tr[data-origpos] > td > .tooltip.in {
  display: none !important;
}
/*修复nice-validator新版下的一处BUG*/
.nice-validator input,
.nice-validator select,
.nice-validator textarea,
.nice-validator [contenteditable] {
  vertical-align: top;
  display: inline-block;
  *display: inline;
  *zoom: 1;
}
/*修复nice-validator和summernote的编辑框冲突*/
.nice-validator .note-editor .note-editing-area .note-editable {
  display: inherit;
}
/*预览区域*/
.plupload-preview,
.faupload-preview {
  padding: 0 10px;
  margin-bottom: 0;
}
.plupload-preview li,
.faupload-preview li {
  margin-top: 15px;
}
.plupload-preview .thumbnail,
.faupload-preview .thumbnail {
  margin-bottom: 10px;
}
.plupload-preview a,
.faupload-preview a {
  display: block;
}
.plupload-preview a:first-child,
.faupload-preview a:first-child {
  height: 90px;
}
.plupload-preview a img,
.faupload-preview a img {
  height: 80px;
  object-fit: cover;
}
.pjax-loader-bar .progress {
  position: fixed;
  top: 0;
  left: 0;
  height: 2px;
  background: #77b6ff;
  box-shadow: 0 0 10px rgba(119, 182, 255, 0.7);
  -webkit-transition: width 0.4s ease;
  transition: width 0.4s ease;
}
.dropdown-menu.text-left a,
.dropdown-menu.text-left li {
  text-align: left !important;
}
.bootstrap-table .fixed-table-loading {
  padding: 10px 0;
}
.bootstrap-table .fixed-table-toolbar .dropdown-menu {
  overflow: inherit;
}
.bootstrap-table .fixed-table-toolbar .columns-right .dropdown-menu {
  overflow: auto;
}
.bootstrap-table .bs-bars .fixed-table-toolbar .dropdown-menu > li:hover > a {
  background-color: #e1e3e9;
  color: #333;
}
.bootstrap-table .fa-toggle-on.fa-2x {
  font-size: 1.86em;
}
.bootstrap-table .form-commonsearch .row > .form-group {
  margin-left: 0;
  margin-right: 0;
}
.bootstrap-table .form-commonsearch .row > .form-group > .control-label {
  white-space: nowrap;
}
.bootstrap-table .btn-commonsearch {
  position: relative;
}
.bootstrap-table .btn-commonsearch > span {
  position: absolute;
  top: -10px;
  right: -10px;
}
.bootstrap-table .table:not(.table-condensed) > tbody > tr > th,
.bootstrap-table .table:not(.table-condensed) > tfoot > tr > th,
.bootstrap-table .table:not(.table-condensed) > thead > tr > td,
.bootstrap-table .table:not(.table-condensed) > tbody > tr > td,
.bootstrap-table .table:not(.table-condensed) > tfoot > tr > td {
  padding: 10px 15px;
  height: 47px;
}
.fixed-table-container tbody td .th-inner,
.fixed-table-container thead th .th-inner {
  padding: 10px 10px;
}
.toolbar {
  margin-top: 10px;
  margin-bottom: 10px;
}
.fixed-table-toolbar .bs-bars,
.fixed-table-toolbar .columns,
.fixed-table-toolbar .search {
  line-height: inherit;
}
.fixed-table-toolbar .toolbar {
  margin-top: 0;
  margin-bottom: 0;
}
.bootstrap-table table tbody tr:first-child td .bs-checkbox {
  vertical-align: middle;
}
.bootstrap-table td.bs-checkbox {
  vertical-align: middle;
}
table.table-nowrap tbody > tr > td,
table.table-nowrap thead > tr > th {
  white-space: nowrap;
}
table.table-nowrap tbody > tr > td.bmdkcol,
 {
  white-space: wrap;
}
.fixed-table-container thead th .sortable {
  padding: 8px 15px;
}
.dropdown-submenu {
  position: relative;
}
.dropdown-submenu > .dropdown-menu {
  overflow: auto;
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
  -webkit-border-radius: 0 6px 6px 6px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 6px 6px 6px;
  -moz-background-clip: padding;
  border-radius: 0 6px 6px 6px;
  background-clip: padding-box;
}
.dropdown-submenu:hover > .dropdown-menu {
  display: block;
}
.dropdown-submenu:hover > a:after {
  border-left-color: #fff;
}
.dropdown-submenu > a:after {
  display: block;
  content: " ";
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #ccc;
  margin-top: 5px;
  margin-right: -10px;
}
.dropdown-submenu.pull-left {
  float: none;
}
.dropdown-submenu.pull-left > .dropdown-menu {
  left: -100%;
  margin-left: 10px;
  -webkit-border-radius: 6px 0 6px 6px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 6px 0 6px 6px;
  -moz-background-clip: padding;
  border-radius: 6px 0 6px 6px;
  background-clip: padding-box;
}
/*重写toast的几个背景色*/
.toast-primary {
  background-color: #48c9b0 !important;
}
.toast-success {
  background-color: #18bc9c !important;
}
.toast-error {
  background-color: #e74c3c !important;
}
.toast-info {
  background-color: #5dade2 !important;
}
.toast-warning {
  background-color: #f1c40f !important;
}
.toast-inverse {
  background-color: #34495e !important;
}
.toast-default {
  background-color: #bdc3c7 !important;
}
#toast-container > div,
#toast-container > div:hover {
  -webkit-box-shadow: 0 0 3px #eee;
  -moz-box-shadow: 0 0 3px #eee;
  box-shadow: 0 0 3px #eee;
}
.layui-layer-fast {
  /*自定义底部灰色操作区*/
}
.layui-layer-fast .layui-layer-title {
  background: #2c3e50 !important;
  color: #fff !important;
  border-bottom: none;
  height: 45px;
  line-height: 45px;
}
.layui-layer-fast .layui-layer-title ~ .layui-layer-setwin {
  top: 0px;
  height: 45px;
}
.layui-layer-fast .layui-layer-title ~ .layui-layer-setwin > a {
  height: 45px;
  line-height: 45px;
  display: inline-block;
}
.layui-layer-fast.layui-layer-border {
  border: none !important;
  box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.3) !important;
}
.layui-layer-fast.layui-layer-iframe {
  overflow: visible;
}
.layui-layer-fast .layui-layer-moves {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}
.layui-layer-fast .layui-layer-btn {
  text-align: center !important;
  padding: 10px !important;
  background: #ecf0f1;
  overflow: hidden;
}
.layui-layer-fast .layui-layer-btn a {
  background-color: #95a5a6;
  color: #fff !important;
  height: 32px;
  line-height: 32px;
  margin-top: 0;
  font-size: 13px;
  border: none;
}
.layui-layer-fast .layui-layer-btn .layui-layer-btn0 {
  background-color: #18bc9c;
  border-color: #18bc9c;
}
.layui-layer-fast .layui-layer-footer {
  padding: 8px 20px;
  background-color: #ecf0f1;
  height: auto;
  min-height: 53px;
  text-align: inherit !important;
}
.layui-layer-fast .layui-layer-confirm {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  bottom: 0;
  border: 1px solid transparent;
  background: transparent;
  color: transparent;
}
.layui-layer-fast .layui-layer-confirm:focus {
  border: 1px solid #444c69;
  -webkit-border-radius: 2px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 2px;
  -moz-background-clip: padding;
  border-radius: 2px;
  background-clip: padding-box;
}
.layui-layer-fast .layui-layer-confirm:focus-visible {
  outline: 0;
}
.layui-layer-fast .layui-layer-tab .layui-layer-title span.layui-this {
  height: 46px;
}
.layui-layer-fast .layui-layer-setwin > a {
  background: none !important;
}
.layui-layer-fast .layui-layer-setwin > a cite {
  display: none;
}
.layui-layer-fast .layui-layer-setwin > a:after {
  content: "\e625";
  font-family: iconfont;
  font-style: normal;
  font-weight: normal;
  text-decoration: inherit;
  position: absolute;
  font-size: 18px;
  color: #fff;
  margin: 0;
  z-index: 1;
}
.layui-layer-fast .layui-layer-setwin > a:hover {
  text-decoration: none !important;
  background: none !important;
}
.layui-layer-fast .layui-layer-setwin > a:focus {
  text-decoration: none !important;
}
.layui-layer-fast .layui-layer-setwin .layui-layer-min {
  display: none;
}
.layui-layer-fast .layui-layer-setwin .layui-layer-min:after {
  content: "\e625";
}
.layui-layer-fast .layui-layer-setwin .layui-layer-max {
  display: none;
}
.layui-layer-fast .layui-layer-setwin .layui-layer-max:after {
  content: "\e623";
}
.layui-layer-fast .layui-layer-setwin .layui-layer-maxmin {
  display: none;
}
.layui-layer-fast .layui-layer-setwin .layui-layer-maxmin:after {
  content: "\e624";
}
.layui-layer-fast .layui-layer-setwin .layui-layer-close1:after {
  content: "\e626";
}
.layui-layer-fast .layui-layer-setwin .layui-layer-close2,
.layui-layer-fast .layui-layer-setwin .layui-layer-close2:hover {
  background: url('../libs/fastadmin-layer/dist/theme/default/icon.png') no-repeat -149px -31px !important;
  top: -30px;
  right: -30px;
}
.layui-layer-fast .layui-layer-setwin .layui-layer-close2:after,
.layui-layer-fast .layui-layer-setwin .layui-layer-close2:hover:after {
  display: none;
}
.layui-layer-content {
  clear: both;
}
.layui-layer-fast-msg {
  min-width: 100px;
}
.layui-layer-fast-tab .layui-layer-title .layui-this {
  color: #333;
}
.layui-layer-fast-tab .layui-layer-content .layui-layer-tabmain {
  margin: 0;
  padding: 0;
}
.input-group > .msg-box.n-right {
  position: absolute;
}
@media (min-width: 564px) {
  body.is-dialog .daterangepicker {
    min-width: 130px;
  }
  body.is-dialog .daterangepicker .ranges ul {
    width: 130px;
  }
}
/*手机版样式*/
@media (max-width: 480px) {
  #firstnav .navbar-custom-menu ul li a {
    padding-left: 10px;
    padding-right: 10px;
  }
  #firstnav .navbar-nav > .user-menu .user-image {
    margin-top: -3px;
  }
  .fixed-table-toolbar > .bs-bars {
    float: none !important;
  }
  .fixed-table-toolbar .toolbar .btn {
    min-height: 33px;
  }
  .fixed-table-toolbar .toolbar a.btn-refresh,
  .fixed-table-toolbar .toolbar a.btn-del,
  .fixed-table-toolbar .toolbar a.btn-add,
  .fixed-table-toolbar .toolbar a.btn-edit,
  .fixed-table-toolbar .toolbar a.btn-import,
  .fixed-table-toolbar .toolbar a.btn-more,
  .fixed-table-toolbar .toolbar a.btn-recyclebin,
  .fixed-table-toolbar .toolbar .btn-mini-xs,
  .fixed-table-toolbar .toolbar .btn-multi {
    font-size: 0;
  }
  .fixed-table-toolbar .toolbar a.btn-refresh .fa,
  .fixed-table-toolbar .toolbar a.btn-del .fa,
  .fixed-table-toolbar .toolbar a.btn-add .fa,
  .fixed-table-toolbar .toolbar a.btn-edit .fa,
  .fixed-table-toolbar .toolbar a.btn-import .fa,
  .fixed-table-toolbar .toolbar a.btn-more .fa,
  .fixed-table-toolbar .toolbar a.btn-recyclebin .fa,
  .fixed-table-toolbar .toolbar .btn-mini-xs .fa,
  .fixed-table-toolbar .toolbar .btn-multi .fa {
    font-size: initial;
  }
  .fixed-table-toolbar .search {
    max-width: 110px;
    float: left !important;
  }
  .fixed .content-wrapper,
  .fixed .right-side {
    padding-top: 50px;
  }
  .main-sidebar,
  .left-side {
    padding-top: 144px;
  }
}
/*平板样式*/
@media (max-width: 767px) {
  .wrapper .main-header .logo {
    border-bottom: 0 solid transparent;
    position: absolute;
    top: 0;
    z-index: 1200;
    width: 130px;
    left: 50%;
    margin-left: -65px;
  }
  .sidebar .mobilenav a.btn-app {
    color: #444;
    width: 100px;
    height: 70px;
    font-size: 13px;
    border: none;
    background: #fff;
  }
  .sidebar .mobilenav a.btn-app i.fa {
    font-size: 24px;
    display: inline-block;
  }
  .sidebar .mobilenav a.btn-app span {
    margin-top: 5px;
    display: block;
  }
  .sidebar .mobilenav a.btn-app.active {
    color: #222d32;
  }
  .wrapper .main-header .navbar .dropdown-menu li > a {
    color: #333;
  }
  .wrapper .main-header .navbar .dropdown-menu li > a:hover {
    background: #eee;
  }
  .wrapper .main-header .navbar .dropdown-menu li.active > a {
    color: #fff;
  }
  .wrapper .main-header .navbar .dropdown-menu li.active > a:hover {
    background: #222d32;
  }
  .main-sidebar,
  .left-side {
    padding-top: 50px;
  }
  .multipletab.multiplenav .main-sidebar {
    padding-top: 95px;
  }
  .n-bootstrap .n-right {
    margin-top: 0;
    top: -20px;
    position: absolute;
    left: 0;
    text-align: right;
    width: 100%;
  }
  .n-bootstrap .n-right .msg-wrap {
    position: relative;
  }
  .n-bootstrap .col-xs-12 > .n-right .msg-wrap {
    margin-right: 15px;
  }
}
/*修复radio和checkbox样式对齐*/
.radio > label,
.checkbox > label {
  margin-right: 10px;
}
.radio > label > input,
.checkbox > label > input {
  margin: 5px 0 0;
}
.wipecache li a {
  color: #444444 !important;
}
/*修正开关关闭下的颜色值*/
.btn-switcher.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.btn-switcher .text-gray {
  color: #d2d6de !important;
}
.jumpto input {
  width: 50px;
  margin-left: 5px;
  margin-right: 5px;
  text-align: center;
  display: inline-block;
}
.fixed-columns,
.fixed-columns-right {
  position: absolute;
  top: 0;
  height: 100%;
  min-height: 41px;
  background-color: #fff;
  box-sizing: border-box;
  z-index: 2;
  box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.08);
}
.fixed-columns .fixed-table-body,
.fixed-columns-right .fixed-table-body {
  min-height: 41px;
  overflow-x: hidden !important;
}
.fixed-columns .fixed-table-body .btn-dragsort,
.fixed-columns-right .fixed-table-body .btn-dragsort {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.fixed-columns {
  left: 0;
}
.fixed-columns-right {
  right: 0;
  box-shadow: -1px 0 8px rgba(0, 0, 0, 0.08);
}
.fix-sticky {
  position: fixed;
  z-index: 100;
}
.fix-sticky thead {
  background: #fff;
}
.fix-sticky thead th,
.fix-sticky thead th:first-child {
  border-left: 0;
  border-right: 0;
  border-bottom: 1px solid #eee;
  border-radius: 0;
}
.sidebar-menu li.active > a > .fa-angle-left,
.sidebar-menu li.active > a > .pull-right-container > .fa-angle-left {
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}
.sidebar-menu li.treeview-open > a > .fa-angle-left,
.sidebar-menu li.treeview-open > a > .pull-right-container > .fa-angle-left {
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.sidebar-menu .treeview-menu > li {
  margin: 4px 0 4px 0;
}
.bootstrap-tagsinput {
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  display: inline-block;
  padding: 4px 6px;
  color: #555;
  vertical-align: middle;
  width: 100%;
  line-height: 23px;
  cursor: text;
}
.bootstrap-tagsinput input {
  border: none;
  box-shadow: none;
  outline: none;
  background-color: transparent;
  padding: 0;
  margin: 0;
  font-size: 13px;
  width: 80px;
  max-width: inherit;
}
.bootstrap-tagsinput input:focus {
  border: none;
  box-shadow: none;
}
.bootstrap-tagsinput .tagsinput-text {
  display: inline-block;
  overflow: auto;
  visibility: hidden;
  height: 1px;
  position: absolute;
  bottom: -1px;
  left: 0;
}
.bootstrap-tagsinput .tag {
  margin-right: 2px;
  color: white;
  min-height: 23px;
}
.bootstrap-tagsinput .tag [data-role="remove"] {
  margin-left: 5px;
  cursor: pointer;
}
.bootstrap-tagsinput .tag [data-role="remove"]:after {
  content: "x";
  padding: 0px 2px;
}
.bootstrap-tagsinput .tag [data-role="remove"]:hover {
  background-color: rgba(255, 255, 255, 0.16);
}
.autocomplete-suggestions {
  border-radius: 2px;
  background: #FFF;
  overflow: auto;
  min-width: 200px;
  -webkit-box-shadow: 0px 20px 30px rgba(83, 88, 93, 0.05), 0px 0px 30px rgba(83, 88, 93, 0.1);
  -moz-box-shadow: 0px 20px 30px rgba(83, 88, 93, 0.05), 0px 0px 30px rgba(83, 88, 93, 0.1);
  box-shadow: 0px 20px 30px rgba(83, 88, 93, 0.05), 0px 0px 30px rgba(83, 88, 93, 0.1);
}
.autocomplete-suggestions strong {
  font-weight: normal;
  color: red;
}
.autocomplete-suggestions .autocomplete-suggestion {
  padding: 5px 10px;
  white-space: nowrap;
  overflow: hidden;
}
.autocomplete-suggestions .autocomplete-selected {
  background: #F0F0F0;
}
.autocomplete-suggestions .autocomplete-group {
  padding: 5px 10px;
}
.autocomplete-suggestions .autocomplete-group strong {
  display: block;
  border-bottom: 1px solid #ddd;
}
.autocontent {
  position: relative;
}
.autocontent .autocontent-caret {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  line-height: 1;
  background: #eee;
  color: #ddd;
  vertical-align: middle;
  padding: 0 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.autocontent .autocontent-caret:hover {
  color: #ccc;
}
/*# sourceMappingURL=backend.css.map */