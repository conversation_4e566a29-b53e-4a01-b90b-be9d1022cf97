<form id="send-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

	<div class="orderbox">
		<h3 class="center topname">订单详情</h3>
		<div class="conbox">
			<div class="tit">
				<span>订单编号：{:$row.ddbh}</span>
				<span>订单日期：{:date('Y-m-d',$row.ordertime)}</span>
				<span>店铺：{:$store ? $store.name : ''}</span>
			</div>
			<div class="conlist flex m30 item-center">
				{if count($data)}
				<div class="itembox">
					<textarea id="input">这是幕后黑手</textarea>
					{foreach name="$data" item="vo"}
					<textarea id="bmdk_{$key}" class="bmdkinput"><?php echo isset($vo['bmdkcon']) ? $vo['bmdkcon'] : '';?></textarea>
					<textarea id="zfdy_{$key}" class="bmdkinput"><?php echo isset($vo['zfdycon']) ? $vo['zfdycon'] : '';?></textarea>
					<div class="item flex item-center">
						<div class="select">
							<input type="checkbox" name="ids[]" value="{$key}">
						</div>
						<div class="img">
							<img src="{:$vo.skuimg}"  alt="">
						</div>
						<div class="text">
							<p><span class="b">定制名称：</span><span class="name">{:$vo.name}</span><span class="btn ml10" onclick="copyText('{:$vo.name}')">复制</span></p>
							<p><span class="b">背面刻字：</span><span class="bmdk">{:$vo.bmdk}</span><span class="btn ml10" onclick="bmdk('<?php echo 'bmdk_'.$key;?>')">复制</span></p>
							<p><span class="b">祝福打印：</span><span class="zfdy">{:$vo.zfdy}</span><span class="btn ml10" onclick="bmdk('<?php echo 'zfdy_'.$key;?>')">复制</span></p>
							<p>
								<span class="b">特殊要求：</span><span class="tsyq">{:$vo.tsyq}</span>
							{if $vo.tsyqimgs}
							{foreach name="$vo.tsyqimgs" item="vo2" key="key2"}
							<img src="{:$vo2}" class="proimg imgicon pointer" onclick="fangda('{:$vo2}');" alt="">
							{/foreach}
							{/if}
							</p>
							<p>
								{if $vo.sfdy}
								<span class="red btn">已打印</span>
								{else}
								<span class="btn" onclick="operate('{:$row.id}','{:$key}','打印');">打印</span>
								{/if}
								{if $vo.sfqg}
								<span class="red btn">已切割</span>
								{else}
								<span class="btn" onclick="operate('{:$row.id}','{:$key}','切割');">切割</span>
								{/if}
							</p>
						</div>
					</div>
					{/foreach}
				</div>
				<div class="addr">
					<p class="b">联系方式：</p>
					<p>{$row.lxfs}</p>
					<p class="b">发货地址：</p>
					<p>
						{$row.khname}<br>
						{$row.khaddr1}<br>
						{$row.city} {$row.state} {$row.zipcode}<br>
						{$row.country}<br>

					</p>
				</div>
				<div class="status">
					{if $row.ifjjlist}
					<span class="red btn">加急</span>
					{/if}
					{if $row.ifdblist}
					<span class="red btn">打包</span>
					{/if}
				</div>
				{/if}
			</div>
			<div class="fot">
				<p><span class="b">状态：</span><span id="status">{:$statusList[$row.status]}</span></p>
				<p><span class="b">物流商：</span><span id="wls">{:$row.wls}</span></p>
				<p><span class="b">物流单号：</span><span id="wldh">{:$row.wldh}</span></p>
			</div>

		</div>
	</div>
	{if count($wljson)}
	<div class="detail">
		<h2>物流详情</h2>
		<div class="translate-section" style="text-align: center; margin-bottom: 20px;">
			<button type="button" id="translate-btn" class="btn btn-info" data-id="{:$row.id}">翻译物流信息</button>
			<span id="translate-loading" style="display: none; margin-left: 10px;">翻译中...</span>
		</div>
		{if $row.wls == $wls[1]}
		<table id="logistics-table" class="table table-striped table-bordered table-hover table-nowrap">
			<tr>
				<td>时间</td>
				<td>位置</td>
				<td>包裹状态</td>
				<td>物流状态</td>
			</tr>
			{foreach name="$wljson.data.0.details" item="vo"}
			<tr>
				<td>{$vo.track_occur_date}</td>
				<td>{$vo.track_location}</td>
				<td>{$vo.track_description}</td>
				<td>{$vo.track_status_cnname}</td>
			</tr>
			{/foreach}
		</table>
		{else}
		<table id="logistics-table" class="table table-striped table-bordered table-hover table-nowrap">
			<tr>
				<td>时间</td>
				<td>位置</td>
				<td>国家</td>
				<td>包裹状态</td>
				<td>物流状态</td>
			</tr>
			{foreach name="$wljson.data.0.events" item="vo"}
			<tr>
				<td>{$vo.eventTime}</td>
				<td>{$vo.location}</td>
				<td>{$vo.country}</td>
				<td>{$vo.activity}</td>
				<td>{$vo.eventCode}</td>
			</tr>
			{/foreach}
		</table>
		{/if}
	</div>
	{/if}


    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
			{if $row.status == 2}
            <button type="button" class="btn btn-primary btn-send" data-id="{:$row.id}">{:__('发货')}</button>
			{elseif  $row.status == 3 || $row.status == 6}
			<button type="button" class="btn btn-primary btn-sendbf" data-id="{:$row.id}">{:__('补发')}</button>
			{/if}
        </div>
    </div>
</form>
<script>
	function copyText(text) {
		var input = document.getElementById("input");
		input.value = text; // 修改文本框的内容

		input.select(); // 选中文本

		document.execCommand("copy"); // 执行浏览器复制命令

		layer.msg("复制成功！");
	}

	function bmdk(id) {
		var input = document.getElementById(id);
		// input.value = document.getElementById(id).innerText; // 修改文本框的内容
		input.select(); // 选中文本

		document.execCommand("copy"); // 执行浏览器复制命令

		layer.msg("复制成功！");
	}

	function fangda(img){
		layer.open({
			type: 1,
			title: '图片',
			fix: true,
			shadeClose: true,
			shade: 0,
			area: ['500px', '500px'],
			content: "<img class='fdimg' src='"+img+"'>"
		});
	}

	// 翻译物流信息功能
	// 等待页面加载完成后绑定事件
	setTimeout(function() {
		// 检查jQuery是否可用
		if (typeof $ !== 'undefined') {
			initTranslateFunction();
		} else {
			// 如果jQuery还没加载，等待一段时间再试
			var checkJQuery = setInterval(function() {
				if (typeof $ !== 'undefined') {
					clearInterval(checkJQuery);
					initTranslateFunction();
				}
			}, 100);
		}
	}, 500);

	function initTranslateFunction() {
		$(document).ready(function() {
			$('#translate-btn').click(function() {
				var orderId = $(this).data('id');
				var $btn = $(this);
				var $loading = $('#translate-loading');

				// 添加调试信息
				console.log('翻译按钮点击 - 订单ID:', orderId);

				// 显示加载状态
				$btn.prop('disabled', true);
				$loading.show();

				$.ajax({
					url: '{:url("order/translate")}',
					type: 'POST',
					data: {
						ids: orderId
					},
					dataType: 'json',
					success: function(response) {
						console.log('翻译响应:', response);
						console.log('响应类型:', typeof response);
						console.log('响应code:', response.code);
						console.log('响应code类型:', typeof response.code);

						if (response.code === 1 || response.code == 1) {
							// 翻译成功，更新表格内容
							console.log('翻译成功，开始更新表格');
							console.log('wljson数据:', response.data.wljson);
							updateLogisticsTable(response.data.wljson);
							if (typeof layer !== 'undefined') {
								layer.msg('翻译成功！');
							} else {
								alert('翻译成功！');
							}
						} else {
							console.error('翻译失败:', response);
							console.error('失败原因 - code:', response.code, 'msg:', response.msg);
							if (typeof layer !== 'undefined') {
								layer.msg('翻译失败：' + (response.msg || '未知错误'));
							} else {
								alert('翻译失败：' + (response.msg || '未知错误'));
							}
						}
					},
					error: function(xhr, status, error) {
						console.error('AJAX请求失败:', {
							status: status,
							error: error,
							responseText: xhr.responseText,
							url: '{:url("order/translate")}',
							data: {ids: orderId}
						});
						if (typeof layer !== 'undefined') {
							layer.msg('翻译请求失败，请稍后重试');
						} else {
							alert('翻译请求失败，请稍后重试');
						}
					},
					complete: function() {
						// 恢复按钮状态
						$btn.prop('disabled', false);
						$loading.hide();
					}
				});
			});
		});
	}

	// 更新物流表格内容
	function updateLogisticsTable(wljson) {
		var $table = $('#logistics-table');
		var $tbody = $table.find('tbody');

		// 如果没有tbody，创建一个
		if ($tbody.length === 0) {
			$tbody = $('<tbody></tbody>');
			$table.append($tbody);
		}

		// 清空现有内容（保留表头）
		$tbody.empty();

		// 根据物流商类型更新表格
		{if $row.wls == $wls[1]}
		// 乐天物流
		if (wljson.data && wljson.data[0] && wljson.data[0].details) {
			$.each(wljson.data[0].details, function(index, item) {
				var row = '<tr>' +
					'<td>' + (item.track_occur_date || '') + '</td>' +
					'<td>' + (item.track_location || '') + '</td>' +
					'<td>' + (item.track_description || '') + '</td>' +
					'<td>' + (item.track_status_cnname || '') + '</td>' +
					'</tr>';
				$tbody.append(row);
			});
		}
		{else}
		// UBI物流
		if (wljson.data && wljson.data[0] && wljson.data[0].events) {
			$.each(wljson.data[0].events, function(index, item) {
				var row = '<tr>' +
					'<td>' + (item.eventTime || '') + '</td>' +
					'<td>' + (item.location || '') + '</td>' +
					'<td>' + (item.country || '') + '</td>' +
					'<td>' + (item.activity || '') + '</td>' +
					'<td>' + (item.eventCode || '') + '</td>' +
					'</tr>';
				$tbody.append(row);
			});
		}
		{/if}
	}
</script>

<style>
	.flex{display: flex;}
	.center{text-align: center;}
	.m30{margin:30px 0;}
	.ml10{margin-left: 10px;}
	.b{font-weight: 900;}
	.conlist{justify-content: space-between;}
	.item-center{align-items: center;}
	.itembox{margin-right: 50px;}
	.itembox .item,.addr{padding: 20px 0;}
	.itembox .img img{margin-right: 50px;max-width:140px;max-height:140px;width:auto;height:auto;object-fit:contain;}
	.topname{margin: 30px auto;font-family: 900;font-size: 40px;}
	.itembox .text p{margin-bottom: 5px;}
	.conbox{padding:20px 50px;}
	.conbox .tit span{margin-right: 20px;padding: 10px 20px;background-color: #000;color: #fff;}
	.btn{padding: 3px 10px;color: #fff;background-color: #000;margin-right: 10px;}
	.btn:hover{color:#fff;background-color: #666;}
	.btn.red{background-color: #f00;}
	.status span{display: block;margin: 20px 0;}
	.conlist{border:1px solid #ccc;padding:0 20px;}
	.fot{border:1px solid #f00;padding: 20px;}
	.select{width: 30px;margin-right: 30px;display: none;}
	#input,.bmdkinput{position: absolute;top: 0;left: 0;opacity: 0;z-index: -10;}
	.imgicon{max-width:35px;max-height:35px;width:auto;height:auto;object-fit:contain;border: 1px solid #eee;border-radius: 3px;}
	.pointer{cursor: pointer;}
	.fdimg{height:calc(100% - 20px);width:auto;display: block;margin: 10px auto;max-width: 100%;max-height: calc(100% - 20px);}

	h2{text-align: center;margin-bottom: 20px;font-size: 36px;line-height: 50px;}
	.detail table td{padding: 10px !important;}
	.btn-info{background-color: #5bc0de;border-color: #46b8da;}
	.btn-info:hover{background-color: #31b0d5;border-color: #269abc;}
	.btn-info:disabled{background-color: #ccc;border-color: #ccc;cursor: not-allowed;}
	.translate-section{border-top: 1px solid #eee;padding-top: 15px;}
</style>
