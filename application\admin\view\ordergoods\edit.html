<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_id" data-rule="required" data-source="order/index" class="form-control selectpage" name="row[order_id]" type="text" value="{$row.order_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Product_sale_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-product_sale_id" data-rule="required" data-source="product/sale/index" class="form-control selectpage" name="row[product_sale_id]" type="text" value="{$row.product_sale_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sku_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sku_id" data-rule="required" data-source="sku/index" class="form-control selectpage" name="row[sku_id]" type="text" value="{$row.sku_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bmdk')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bmdk" class="form-control" name="row[bmdk]" type="text" value="{$row.bmdk|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Zfdy')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zfdy" class="form-control" name="row[zfdy]" type="text" value="{$row.zfdy|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tsyqimages')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-tsyqimages" class="form-control" size="50" name="row[tsyqimages]" type="text" value="{$row.tsyqimages|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-tsyqimages" class="btn btn-danger faupload" data-input-id="c-tsyqimages" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-tsyqimages"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-tsyqimages" class="btn btn-primary fachoose" data-input-id="c-tsyqimages" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-tsyqimages"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-tsyqimages"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tsyq')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tsyq" class="form-control" name="row[tsyq]" type="text" value="{$row.tsyq|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sfdy')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-sfdy" class="form-control selectpicker" name="row[sfdy]">
                {foreach name="sfdyList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.sfdy"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sfqg')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-sfqg" class="form-control selectpicker" name="row[sfqg]">
                {foreach name="sfqgList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.sfqg"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" data-source="store/index" class="form-control selectpage" name="row[store_id]" type="text" value="{$row.store_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ordertime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ordertime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[ordertime]" type="text" value="{:$row.ordertime?datetime($row.ordertime):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
