<?php

namespace app\admin\model;

use think\Model;


class Order extends Model
{





    // 表名
    protected $name = 'order';

    // 完整表名
    protected $table = 'fa_order';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text',
        'ifjjlist_text',
        'ordertime_text',
        'ifdblist_text',
		'ifbf_text'
    ];



    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3'), '4' => __('Status 4'), '5' => __('Status 5'), '6' => __('Status 6')];
    }

	public function getIfbfList()
	{
	    return ['0' => __('Ifbf 0'), '1' => __('Ifbf 1')];
	}

    public function getIfjjlistList()
    {
        return ['0' => __('Ifjjlist 0'), '1' => __('Ifjjlist 1')];
    }

    public function getIfdblistList()
    {
        return ['0' => __('Ifdblist 0'), '1' => __('Ifdblist 1')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

	public function getIfbfTextAttr($value, $data)
	{
	    $value = $value ? $value : (isset($data['ifbf']) ? $data['ifbf'] : '');
	    $list = $this->getIfbfList();
	    return isset($list[$value]) ? $list[$value] : '';
	}


    public function getIfjjlistTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['ifjjlist']) ? $data['ifjjlist'] : '');
        $list = $this->getIfjjlistList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getOrdertimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['ordertime']) ? $data['ordertime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getIfdblistTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['ifdblist']) ? $data['ifdblist'] : '');
        $list = $this->getIfdblistList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setOrdertimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function admin()
    {
        return $this->belongsTo('Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

	public function store()
	{
	    return $this->belongsTo('Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
	}
	public function wuliu()
	{
	    return $this->belongsTo('Wuliu', 'wuliu_id', 'id', [], 'LEFT')->setEagerlyType(0);
	}
}
