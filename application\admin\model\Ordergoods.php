<?php

namespace app\admin\model;

use think\Model;


class Ordergoods extends Model
{

    

    

    // 表名
    protected $name = 'ordergoods';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'sfdy_text',
        'sfqg_text',
        'ordertime_text',
		'status_text',
    ];
    

    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3'), '4' => __('Status 4'), '5' => __('Status 5'), '6' => __('Status 6')];
    }
	
    public function getSfdyList()
    {
        return ['0' => __('Sfdy 0'), '1' => __('Sfdy 1')];
    }

    public function getSfqgList()
    {
        return ['0' => __('Sfqg 0'), '1' => __('Sfqg 1')];
    }

	public function getStatusTextAttr($value, $data)
	{
	    $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
	    $list = $this->getStatusList();
	    return isset($list[$value]) ? $list[$value] : '';
	}
	
    public function getSfdyTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['sfdy']) ? $data['sfdy'] : '');
        $list = $this->getSfdyList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getSfqgTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['sfqg']) ? $data['sfqg'] : '');
        $list = $this->getSfqgList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getOrdertimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['ordertime']) ? $data['ordertime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setOrdertimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function order()
    {
        return $this->belongsTo('Order', 'order_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function store()
    {
        return $this->belongsTo('Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function sale()
    {
        return $this->belongsTo('app\admin\model\product\Sale', 'product_sale_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
