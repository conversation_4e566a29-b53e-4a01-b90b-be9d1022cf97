<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {if condition="$store_id"}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store.name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" class="form-control" name="row[store_id]" type="hidden" value="{$store_id}">
            <input class="form-control" type="text" value="{$store.name}" disabled>
        </div>
    </div>
    {else /}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store.name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" data-source="store/index" data-field="name" class="form-control selectpage" name="row[store_id]" type="text" value="">
        </div>
    </div>
    {/if}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" class="form-control" step="0.01" name="row[money]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['0'=>__('Status 0'), '1'=>__('Status 1')], 0)}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
