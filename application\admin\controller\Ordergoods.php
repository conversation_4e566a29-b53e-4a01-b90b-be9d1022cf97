<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
/**
 * 订单产品
 *
 * @icon fa fa-circle-o
 */
class Ordergoods extends Backend
{

    /**
     * Ordergoods模型对象
     * @var \app\admin\model\Ordergoods
     */
    protected $model = null;
	protected $searchFields = 'name';
	protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Ordergoods;
        $this->view->assign("sfdyList", $this->model->getSfdyList());
        $this->view->assign("sfqgList", $this->model->getSfqgList());
		$this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['order','store','sale'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('order')->visible(['ddbh','ifjjlist','ifdblist']);
				$row->getRelation('store')->visible(['name']);
				$row->getRelation('sale')->visible(['name']);
				$product_row = Db::table('fa_product_sale')->where('id',$row['product_sale_id'])->find();
				$imgs = explode(",",$product_row['stylelist']);
				$row['sku_id'] = $imgs[$row['sku_id']];
				$count = $this->model->where('order_id',$row['order_id'])->where('status','<',2)->count();
				$row['iffh'] = $count ?: 0;
				$allcount = $this->model->where('order_id',$row['order_id'])->count();
				$row['xh'] = ($allcount - $count) .'/'. $allcount;
				$this->model->where('status',0)->where('sfdy',1)->where('sfqg',1)->update(['status'=>1]);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }
	
	
	public function operate($type = null , $ids = null)
	{
		$row = $this->model->get($ids);
		if (!$row) {
		    $this->error(__('No Results were found'));
		}
		$order = Db::table('fa_order')->where('id',$row['order_id'])->find();
		$datajson = json_decode($order['datajson'],true);
		
		if($type == 1){
			if($row['sfqg'] == 1){
				$row['status'] = 1;
			}
			$result = $this->model->where('id',$ids)->update(['sfdy'=>1,'status'=>$row['status']]);
			foreach($datajson as $k => $list){
				$sn = $order['id'].'_'.$k;
				if($sn == $row['sn']){
					$datajson[$k]['sfdy'] = 1;
				}
			}
		}elseif($type == 2){
			if($row['sfdy'] == 1){
				$row['status'] = 1;
			}
			$result = $this->model->where('id',$ids)->update(['sfqg'=>1,'status'=>$row['status']]);
			foreach($datajson as $k => $list){
				$sn = $order['id'].'_'.$k;
				if($sn == $row['sn']){
					$datajson[$k]['sfqg'] = 1;
				}
			}
		}elseif($type == 3){
			$result = $this->model->where('id',$ids)->update(['status'=>2]);
		}
		if (false === $result) {
		    $this->error(__('No rows were updated'));
		}
		$wwc = 0;
		foreach($datajson as $k => $list){
			if($list['sfdy'] == 0 || $list['sfqg'] == 0){
				$wwc ++;
			}
		}
		if(!$wwc && $order['khname'] && $order['status'] == 0){
			$status = 2;
		}elseif($wwc && $order['khname'] && $order['status'] == 0){
			$status = 1;
		}else{
			$status = $order['status'];
		}
		
		Db::table('fa_order')->where('id',$row['order_id'])->update(['datajson'=>json_encode($datajson),'status'=>$status]);
		
		$this->success();
	}
	
	public function send($ids = null)
	{
		$row = $this->model->get($ids);
		$order_row = Db::table('fa_order')->where('id',$row['order_id'])->find();
		if (!$order_row) {
		    $this->error(__('No Results were found'));
		}
		if (false === $this->request->isPost()) {
			if($order_row['status'] == 3){
				$this->error(__('该订单已发货'));
			}
		    return $this->view->fetch();
		}
		$params = $this->request->post('row/a');
		if($params['wldh'] == false){
			$this->error(__('物流单号不得为空'));
		}
		$params['status'] = 3;
		$params['fhtime'] = time();
		$result = Db::table('fa_order')->where('id',$row['order_id'])->update($params);
		$this->model->where('order_id',$row['order_id'])->update(['status'=>3]);
		
		//头程
		$today = strtotime(date('Y-m-d',time()));
		$tcgl_row = Db::table('fa_tcgl')->where('createtime',$today)->where('wls',$params['wls'])->find();
		if(!$tcgl_row){
			Db::table('fa_tcgl')->insert([
				'admin_id' => $this->auth->id,
				'wls' => $params['wls'],
				'order_ids' => $row['order_id'],
				'createtime' => $today
			]);
		}else{
			$oids = explode(',',$tcgl_row['order_ids']);
			if(!in_array($row['order_id'],$oids)){
				Db::table('fa_tcgl')->where('createtime',$today)->where('wls',$params['wls'])->update([
					'order_ids' => $tcgl_row['order_ids'].','.$row['order_id']
				]);
			}
			
		}
		
		
		if (false === $result) {
		    $this->error(__('No rows were updated'));
		}
		$this->success('发货成功');
	}
	
	public function copy($ids = null, $field = null)
	{
		$row = $this->model->get($ids);
		if (!$row) {
		    $this->error(__('No Results were found'));
		}
		$order = Db::table('fa_order')->where('id',$row['order_id'])->find();
		$datajson = json_decode($order['datajson'],true);
		$con = '';
		if(count($datajson)){
			foreach($datajson as $k => $v){
				$sn = $order['id'].'_'.$k;
				if($sn == $row['sn']){
					$con = isset($v[$field.'con']) ? $v[$field.'con'] : '';
				}
			}
		}
		$this->view->assign('con', html_entity_decode($con));
		return $this->view->fetch();
	}

}
