<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 店铺打款记录
 *
 * @icon fa fa-money
 */
class StoreMoneyLog extends Backend
{
    /**
     * StoreMoneyLog模型对象
     * @var \app\admin\model\StoreMoneyLog
     */
    protected $model = null;

    /**
     * 关联查询
     */
    protected $relationSearch = true;

    /**
     * 快速搜索字段
     */
    protected $searchFields = 'id,store.name';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\StoreMoneyLog;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            // 获取店铺ID
            $store_id = $this->request->param('store_id', 0);

            // 构建查询参数
            $filter = $this->request->get("filter", '');
            $filter = (array)json_decode($filter, true);
            if ($store_id) {
                $filter['store_id'] = $store_id;
            }
            $this->request->get(['filter' => json_encode($filter)]);

            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['store'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            // 将模型集合转换为数组
            $items = collection($list->items())->toArray();
            $result = array("total" => $list->total(), "rows" => $items);

            return json($result);
        }

        // 获取店铺ID和名称
        $store_id = $this->request->param('store_id', 0);
        if ($store_id) {
            $store = \app\admin\model\Store::get($store_id);
            $this->view->assign('store', $store);
        }

        $this->view->assign('store_id', $store_id);
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }

                $result = false;
                Db::startTrans();
                try {
                    // 设置时间戳
                    $params['add_time'] = time();
                    $params['update_time'] = time();

                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        // 获取店铺ID
        $store_id = $this->request->param('store_id', 0);
        if ($store_id) {
            $store = \app\admin\model\Store::get($store_id);
            $this->view->assign('store', $store);
        }

        $this->view->assign('store_id', $store_id);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $result = false;
                Db::startTrans();
                try {
                    // 更新时间戳
                    $params['update_time'] = time();

                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        // 获取关联店铺
        $store = $row->store;
        $this->view->assign('store', $store);
        $this->view->assign('row', $row);
        return $this->view->fetch();
    }
}
