<?php

namespace app\admin\model;

use think\Model;


class Tcgl extends Model
{

    

    

    // 表名
    protected $name = 'tcgl';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'fhtime_text'
    ];
    

    



    public function getFhtimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['fhtime']) ? $data['fhtime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setFhtimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function admin()
    {
        return $this->belongsTo('Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
