define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'wuliu/index' + location.search,
                    add_url: 'wuliu/add',
                    edit_url: 'wuliu/edit',
                    del_url: 'wuliu/del',
                    multi_url: 'wuliu/multi',
                    import_url: 'wuliu/import',
                    table: 'wuliu',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'admin.nickname', title: __('Admin.nickname'), operate: 'LIKE'},
                        {field: 'order.ddbh', title: __('Order.ddbh'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
						{field: 'order.wls', title: __('物流商'), operate: 'LIKE'},
						{field: 'order.wldh', title: __('物流单号'), operate: 'LIKE'},
                        // {field: 'store.name', title: __('Store.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
						{field: 'wltxt', title: __('物流状态')},
                        {field: 'text', title: __('Text'), operate: 'LIKE'},
                        {field: 'info', title: __('Info'), operate: 'LIKE'},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1'),"2":__('Status 2'),"3":__('Status 3'),"4":__('Status 4')}, formatter: Table.api.formatter.status},
						{field: 'wllist', title: __('Wllist'), searchList: {"0":__('Wllist 0'),"1":__('Wllist 1'),"2":__('Wllist 2')}, formatter: Table.api.formatter.normal},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        
                        {
                            field: 'operate',
                            width: "150px",
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                
                                {
                                    name: 'detail',
                        			text: __('详情'),
                                    title: __('详情'),
                                    classname: 'btn btn-xs btn-primary btn-dialog',
                                    icon: 'fa fa-list',
                        			extend:'data-area=["90%","90%"]',
                                    url: 'wuliu/detail',
                                    callback: function (data) {
                                        Layer.msg("订单已发货");
                        				return false;
                                    }
                                },
								{
								    name: 'ajax',
									text: __('赔偿'),
								    title: __('赔偿'),
								    classname: 'btn btn-xs btn-warning btn-ajax',
								    icon: 'fa fa-list',
									confirm: __('确定赔偿该订单吗？'),
								    url: 'wuliu/peichang',
									visible :function(row){
										if(row.status == 3){
											return true;
										}else{
											return false;
										}
									},
								    success: function (data, ret) {
										layer.msg(__('订单已赔偿！'));
										table.bootstrapTable('refresh', {});
								        return false;
								    },
								    error: function (data, ret) {
								        console.log(data, ret);
								        Layer.alert(ret.msg);
								        return false;
								    }
								},
                        		
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });
			
			$(document).on("click", ".btn-require", function () {
			    //在table外不可以使用添加.btn-change的方法
			    //只能自己调用Table.api.multi实现
			    //如果操作全部则ids可以置为空
			    var ids = Table.api.selectedids(table);
			    Table.api.multi("changestatus", ids.join(","), table, this);
			});

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
