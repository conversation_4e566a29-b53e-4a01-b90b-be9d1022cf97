define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'kesu/index' + location.search,
                    add_url: 'kesu/add',
                    edit_url: 'kesu/edit',
                    del_url: 'kesu/del',
                    multi_url: 'kesu/multi',
                    import_url: 'kesu/import',
                    table: 'kesu',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 2,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
						{field: 'admin.nickname', title: __('Admin.nickname'), operate: 'LIKE'},
						{field: 'store.name', title: __('Store.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
						{field: 'order.ddbh', title: __('Order.ddbh'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'ksbh', title: __('Ksbh'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'jltime', title: __('Jltime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'kstxt', title: __('Kstxt'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
						{field: 'ksimages', title: __('Ksimages'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.images},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1')}, formatter: Table.api.formatter.status},
						{field: 'kesulog.gjtext', title: __('跟进内容'), operate: 'LIKE'},
						{field: 'kesulog.operate_type', title: __('处理方式'), operate: 'LIKE'},
						{field: 'kesulog.operate_con', title: __('处理详情'), operate: 'LIKE'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                
                                {
                                	name: 'detail',
                                	text: __('跟进日志'),
                                	title: __('跟进日志'),
                                	classname: 'btn btn-xs btn-success btn-dialog btn-detail',
                                	extend:'data-area=["90%","90%"]',
                                	icon: 'fa fa-hourglass-half',
                                	url: 'kesulog/add/kesu_id/{ids}',
                                	callback: function (data) {
                                		Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                	},
                                	visible: function (row) {
                                		//返回true时按钮显示,返回false隐藏
                                		return true;
                                	}
                                },
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
