<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	<div class="panel panel-default panel-info">
		<div class="panel-heading">
			<div class="panel-lead">
				<span style="font-weight: bold;font-style: normal;">客诉跟进</span>
			</div>
		</div>
		<div class="panel-body">
			{if !$kesu_id}
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Kesu_id')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-kesu_id" data-rule="required" data-source="kesu/index" data-field="ksbh" class="form-control selectpage" name="row[kesu_id]" type="text" value="">
				</div>
			</div>
			{else}
			<input id="c-kesu_id" class="form-control" name="row[kesu_id]"  type="hidden" value="{$kesu_id}">
			{/if}
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Gjtime')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-gjtime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm" data-use-current="true" name="row[gjtime]" type="text" value="{:date('Y-m-d H:i')}">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Gjtext')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-gjtext" data-rule="required" class="form-control" name="row[gjtext]" type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
				<div class="col-xs-12 col-sm-8">
					
					<div class="radio">
					{foreach name="statusList" item="vo"}
					<label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="0"}checked{/in} /> {$vo}</label> 
					{/foreach}
					</div>

				</div>
			</div>
			<div class="form-group" data-favisible="status=1">
				<label class="control-label col-xs-12 col-sm-2">{:__('Operate_type')}:</label>
				<div class="col-xs-12 col-sm-8">
					<select  id="c-operate_type" data-rule="" class="form-control selectpicker" name="row[operate_type]">
						{foreach name="$site.ks_opetype" item="vo"}
							<option value="{$vo}" {in name="vo" value=""}selected{/in}>{$vo}</option>
						{/foreach}
					</select>
				</div>
			</div>
			<div class="form-group" data-favisible="status=1">
				<label class="control-label col-xs-12 col-sm-2">{:__('Operate_con')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-operate_con" class="form-control" name="row[operate_con]" type="text">
				</div>
			</div>
		</div>
	</div>
	
	<div class="panel panel-default panel-info">
		<div class="panel-heading">
			<div class="panel-lead">
				<span style="font-weight: bold;font-style: normal;">跟进日志</span>
			</div>
		</div>
	    <div class="panel-body">
	        <div id="myTabContent" class="tab-content">
	            <div class="tab-pane fade active in" id="one">
	                <div class="widget-body no-padding">
	                    <div id="toolbar1" class="toolbar">
	                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
	                    </div>
	                    <table id="table1" class="table table-striped table-bordered table-hover table-nowrap phonelogtable"
	                           data-operate-edit=""
	                           data-operate-del="{:$auth->check('kesulog/del')}"
	                           width="100%">
	                    </table>
	                </div>
	            </div>
	
	        </div>
	    </div>
	</div>
	
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
