define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'product/sale/index' + location.search,
                    add_url: 'product/sale/add',
                    edit_url: 'product/sale/edit',
                    del_url: 'product/sale/del',
                    multi_url: 'product/sale/multi',
                    import_url: 'product/sale/import',
                    table: 'product_sale',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'admin.nickname', title: __('Admin.nickname'), operate: 'LIKE'},
						{field: 'name', title: __('Name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
						// {field: 'product_design_id', title: __('Product_design_id')},
						{field: 'images', title: __('Images'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.images},
						{field: 'stylelist', title: __('Stylelist'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.images},
						{field: 'info', title: __('Info'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'design.name', title: __('Design.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'store.name', title: __('Store.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'designer.name', title: __('Designer.name'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {field: 'salenum', title: __('总销量'), operate: false,sortable:true},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
						 {field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1')}, formatter: Table.api.formatter.status},
                        
                        {
                            field: 'operate',
                            width: "150px",
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                             buttons: [
                                 
                                 {
                                     name: 'ajax',
                                     title: __('奖励'),
									 text: __('奖励'),
                                     classname: 'btn btn-xs btn-success btn-magic btn-ajax',
                                     icon: 'fa fa-magic',
                                     confirm: '确认已经发放奖励吗？',
                                     url: 'product/sale/jiangli',
									 visible :function(row){
									 	if(row.status == 0 && row.salenum >= Config.jlnum){
									 		return true;
									 	}else{
									 		return false;
									 	}
									 },
                                     success: function (data, ret) {
                                         Layer.msg(ret.msg);
                                         //如果需要阻止成功提示，则必须使用return false;
										 table.bootstrapTable('refresh', {});
                                         // return false;
                                     },
                                     error: function (data, ret) {
                                         console.log(data, ret);
                                         Layer.alert(ret.msg);
                                         return false;
                                     }
                                 },
                             ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
