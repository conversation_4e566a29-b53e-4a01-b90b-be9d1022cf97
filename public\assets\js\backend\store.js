define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'store/index' + location.search,
                    add_url: 'store/add',
                    edit_url: 'store/edit',
                    del_url: 'store/del',
                    multi_url: 'store/multi',
                    import_url: 'store/import',
                    table: 'store',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'address', title: __('Address'), operate: 'LIKE'},
                        {field: 'info', title: __('Info'), operate: 'LIKE'},
						{field: 'dbdp', title: __('Dbdp'), operate: 'LIKE'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'status', title: __('打款状态'), searchList: {"0":__('未打款'),"1":__('已打款')}, formatter: function(value, row, index) {
                            // 检查latestmoneylog是否存在且不为null
                            if ( row.status !== null) {
                                // 检查money_log_id是否存在，确认有打款记录
                                if (row.status!=null) {
                                    // 检查status是否存在且不为undefined
                                    if (row.status !== undefined && row.status !== null) {
                                        var statusClass = parseInt(row.status) === 1 ? 'success' : 'danger';
                                        var statusText = parseInt(row.status) === 1 ? __('已打款') : __('未打款');
                                        return '<span class="label label-' + statusClass + '">' + statusText + '</span>';
                                    }
                                }
                            }
                            // 如果没有打款记录或status不存在
                            return '<span class="label label-default">' + __('无记录') + '</span>';
                        }},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, buttons: [
                            {
                                name: 'money_log',
                                text: __('打款记录'),
                                title: __('打款记录'),
                                classname: 'btn btn-xs btn-info btn-dialog',
                                icon: 'fa fa-money',
                                url: 'store_money_log/index?store_id={id}',
                                extend: 'data-area=\'["80%", "75%"]\'',
                                callback: function (data) {
                                    table.bootstrapTable('refresh');
                                }
                            }
                        ], formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
