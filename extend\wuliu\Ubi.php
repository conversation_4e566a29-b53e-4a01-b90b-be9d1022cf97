<?php

/*
 * Created By PhpStorm
 * File: Ubi.php
 * User: 尊杨科技
 * Date: 2025/5/27
 */

namespace wuliu;

class Ubi {
//    const WALLTECH_SERVER = 'http://cn.etowertech.com';
//    const ACCESS_TOKEN = 'pclaZC6FULwr7EBS3KvZRp';
//    const SECRET_KEY = 'rFPaclcPiLSqQVJKPVdjQA';

    const WALLTECH_SERVER = 'http://qa.etowertech.com';
    const ACCESS_TOKEN = 'testqMfBG8xvSKJKT6O2Pz9';
    const SECRET_KEY = 'H2daH9s2IJmCQYQ77oEiAQ';
    private $walltechDate;

    public function __construct()
    {
        $this->walltechDate = date(DATE_RFC7231, time() - 60 * 60 * 8);
    }

    private function buildHeaders($method, $path, $acceptType = 'application/json')
    {
        // 重新计算时间戳，确保每次请求都是最新的
        $walltechDate = date(DATE_RFC7231, time() - 60 * 60 * 8);
        $auth = $method . "\n" . $walltechDate . "\n" . $path;
        $hash = base64_encode(hash_hmac('sha1', $auth, self::SECRET_KEY, true));

        return [
            'Content-Type: application/json',
            'Accept: ' . $acceptType,
            'X-WallTech-Date: ' . $walltechDate,
            'Authorization: WallTech ' . self::ACCESS_TOKEN . ':' . $hash
        ];
    }

    private function sendRequest($method, $headers, $body)
    {

        $ch = curl_init(self::WALLTECH_SERVER . $method);
        if($body==null){
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        }else{
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
        }

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        return curl_exec($ch);
    }

    public function createOrder($orderData)
    {
        $method = '/services/shipper/orders';
        $headers = $this->buildHeaders('POST', self::WALLTECH_SERVER . $method);
        $body = json_encode($orderData);
        return $this->sendRequest($method, $headers, $body);
    }

    public function printLabel($referenceNos)
    {
        $method = '/services/shipper/labels';
        $headers = $this->buildHeaders('POST', self::WALLTECH_SERVER . $method);
        $body = json_encode($referenceNos);

        return $this->sendRequest($method, $headers, $body);
    }

    public function forecast($referenceNos)
    {
        $method = '/services/integration/shipper/manifests';
        $headers = $this->buildHeaders('POST', self::WALLTECH_SERVER . $method);
        $body = json_encode($referenceNos);

        return $this->sendRequest($method, $headers, $body);
    }

    public function track($trackingNumbers)
    {
        $method = '/services/integration/shipper/trackingEvents';
        $headers = $this->buildHeaders('POST', self::WALLTECH_SERVER . $method);
        $body = json_encode($trackingNumbers);

        return $this->sendRequest($method, $headers, $body);
    }

    public function pod($podId)
    {
        $method = '/services/integration/shipper/proof-of-delivery/' . $podId;
        $headers = $this->buildHeaders('GET', self::WALLTECH_SERVER . $method);
        return $this->sendRequest($method, $headers, null);
    }

    /**
     * 获取已开通的服务类型
     * @return bool|string
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2025/5/28
     */
    public function getServicesCateLog(){
        $method='/services/shipper/service-catalog';
        $headers=$this->buildHeaders('GET', self::WALLTECH_SERVER . $method);
        return $this->sendRequest($method,$headers, null);
    }
}