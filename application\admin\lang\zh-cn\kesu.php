<?php

return [
    'Id'             => 'ID',
    'Admin_id'       => '录入员工',
    'Order_id'       => '订单',
    'Ksbh'           => '客诉编号',
    'Store_id'       => '店铺',
    'Jlname'         => '记录人',
    'Jltime'         => '记录时间',
    'Kstxt'          => '客诉原因',
    'Opetype'        => '处理方式',
    'Opetype 0'      => '退款',
    'Opetype 1'      => '换货',
    'Gjname'         => '跟进人',
    'Gjlist'         => '跟进状态',
    'Gjlist 0'       => '待跟进',
    'Gjlist 1'       => '跟进中',
    'Gjlist 2'       => '已完成',
    'Status'         => '状态',
    'Status 0'       => '待处理',
    'Set status to 0'=> '设为待处理',
    'Status 1'       => '已处理',
    'Set status to 1'=> '设为已处理',
    'Createtime'     => '创建时间',
    'Updatetime'     => '更新时间',
    'Admin.nickname' => '昵称',
    'Store.name'     => '店铺名称',
    'Order.ddbh'     => '订单编号',
	'Ksimages'     => '客诉截图'
];
