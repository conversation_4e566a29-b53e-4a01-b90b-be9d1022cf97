<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

	<div class="form-group">
	    <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
	    <div class="col-xs-12 col-sm-8">
	        <input id="c-store_id" data-rule="required" data-source="store/index" class="form-control selectpage" name="row[store_id]" type="text" value="">
	    </div>
	</div>
	<div class="form-group">
	    <label class="control-label col-xs-12 col-sm-2">{:__('表格上传')}:</label>
	    <div class="col-xs-12 col-sm-8">
	        <div class="input-group">
	            <input id="c-images" data-rule="required" class="form-control" size="50" name="row[file]" type="text" value="">
	            <div class="input-group-addon no-border no-padding">
	                <span><button type="button" id="faupload-images" class="btn btn-danger faupload" data-input-id="c-images" data-mimetype="xls,xlsx" data-multiple="false" data-preview-id="p-images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
	                <span><button type="button" id="fachoose-images" class="btn btn-primary fachoose" data-input-id="c-images" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
	            </div>
	            <span class="msg-box n-right" for="c-images"></span>
	        </div>
	        <ul class="row list-inline faupload-preview" id="p-images"></ul>
	    </div>
	</div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
