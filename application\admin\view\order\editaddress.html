<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>编辑联系方式和收货地址</em></div>
    </div>
    <div class="panel-body">
        <form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="" novalidate>
            {:token()}
            <input type="hidden" name="row[id]" value="{$row.id}">
            <!-- 确保所有字段都会被提交，即使为空 -->
            <script>
                $(function() {
                    // 禁用表单验证
                    $("#edit-form").validator('destroy');

                    // 表单提交前处理
                    $("#edit-form").on('submit', function(e) {
                        // 阻止默认提交行为
                        e.preventDefault();

                        console.log("表单提交前处理");

                        // 创建一个新的FormData对象
                        var formData = new FormData(this);

                        // 确保所有字段都有值，即使是空字符串
                        if (!formData.has('row[khname]')) {
                            formData.append('row[khname]', '');
                        }
                        if (!formData.has('row[khaddr1]')) {
                            formData.append('row[khaddr1]', '');
                        }
                        if (!formData.has('row[khaddr2]')) {
                            formData.append('row[khaddr2]', '');
                        }
                        if (!formData.has('row[city]')) {
                            formData.append('row[city]', '');
                        }
                        if (!formData.has('row[state]')) {
                            formData.append('row[state]', '');
                        }
                        if (!formData.has('row[country]')) {
                            formData.append('row[country]', '');
                        }
                        if (!formData.has('row[zipcode]')) {
                            formData.append('row[zipcode]', '');
                        }

                        // 使用AJAX手动提交表单
                        $.ajax({
                            url: $(this).attr('action'),
                            type: 'POST',
                            data: $(this).serialize(),
                            dataType: 'json',
                            success: function(ret) {
                                console.log('表单提交成功，返回数据:', ret);
                                if (ret.hasOwnProperty("code") && ret.code === 1) {
                                    // 关闭当前窗口
                                    parent.Layer.closeAll();
                                    // 显示成功消息
                                    parent.Layer.msg(ret.msg || '保存成功');
                                    // 刷新父页面的表格
                                    parent.$("#table").bootstrapTable('refresh');
                                    // 如果是在自定义列表视图中，也刷新自定义列表
                                    if (typeof parent.loadOrderListData === 'function') {
                                        parent.loadOrderListData(1);
                                    }
                                    // 返回数据给父窗口
                                    Fast.api.close({
                                        result: true,
                                        message: ret.msg || '保存成功',
                                        code: 1
                                    });
                                } else {
                                    Layer.alert(ret.msg || '保存失败');
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('表单提交失败:', error);
                                Layer.alert('提交失败: ' + error);
                            }
                        });
                    });
                });
            </script>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('联系方式')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-khname" class="form-control" name="row[khname]" type="text" value="{$row.khname|htmlentities}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('收货地址1')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-khaddr1" class="form-control" name="row[khaddr1]" type="text" value="{$row.khaddr1|htmlentities}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('收货地址2')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-khaddr2" class="form-control" name="row[khaddr2]" type="text" value="{$row.khaddr2|htmlentities}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('城市')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-city" class="form-control" name="row[city]" type="text" value="{$row.city|htmlentities}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('州/省')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-state" class="form-control" name="row[state]" type="text" value="{$row.state|htmlentities}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('国家')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-country" class="form-control" name="row[country]" type="text" value="{$row.country|htmlentities}">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('邮编')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <input id="c-zipcode" class="form-control" name="row[zipcode]" type="text" value="{$row.zipcode|htmlentities}">
                </div>
            </div>
            <div class="form-group layer-footer">
                <label class="control-label col-xs-12 col-sm-2"></label>
                <div class="col-xs-12 col-sm-8">
                    <button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
                    <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                </div>
            </div>
        </form>
    </div>
</div>
