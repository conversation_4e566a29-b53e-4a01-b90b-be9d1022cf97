<?php

namespace app\admin\model\product;

use think\Model;


class Sale extends Model
{

    

    

    // 表名
    protected $name = 'product_sale';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'stylelist_text',
		'status_text'
    ];
    

    
    public function getStylelistList()
    {
        return ['1' => __('Stylelist 1'), '2' => __('Stylelist 2'), '3' => __('Stylelist 3'), '4' => __('Stylelist 4'), '5' => __('Stylelist 5'), '6' => __('Stylelist 6')];
    }
	
	public function getStatusList()
	{
	    return ['0' => __('Status 0'), '1' => __('Status 1')];
	}


    public function getStylelistTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['stylelist']) ? $data['stylelist'] : '');
        $list = $this->getStylelistList();
        return isset($list[$value]) ? $list[$value] : '';
    }

	public function getStatusTextAttr($value, $data)
	{
	    $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
	    $list = $this->getStatusList();
	    return isset($list[$value]) ? $list[$value] : '';
	}


    public function admin()
    {
        return $this->belongsTo('app\admin\model\Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function design()
    {
        return $this->belongsTo('Design', 'product_design_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function store()
    {
        return $this->belongsTo('app\admin\model\Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function designer()
    {
        return $this->belongsTo('app\admin\model\Designer', 'designer_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
