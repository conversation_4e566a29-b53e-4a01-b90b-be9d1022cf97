<?php

namespace app\api\controller;

use app\common\controller\Api;
use wuliu\Ubi;

/**
 * 首页接口
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 首页
     *
     */
    public function index()
    {
        $ubi=new Ubi();
       // $info=$ubi->getServicesCateLog();
        $arr=[
            'orderIds'=>[
                'LP207116009FR'
            ],
            'labelType'=>1,
            'packinglist'=>false,
            'labelFormat'=>"PDF",
            'dpi'=>"300"
        ];
        $info=$ubi->printLabel($arr);

        // 先解析外层JSON
        $decoded = json_decode($info, true);

        // 如果data字段存在且是字符串，再次解析
        if (isset($decoded['data']) && is_string($decoded['data'])) {
            $innerData = json_decode($decoded['data'], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $decoded['data'] = $innerData;
            }
        }

        $this->success('请求成功', $decoded);
    }
}
