<?php
/*
* Created By PhpStorm
* File: BaiduTranslate.php
* User: 尊杨科技
* Date: 2025/5/14
*/

namespace translate;

class BaiduTranslate
{
    // 定义常量
    const CURL_TIMEOUT = 10;
    const URL = "http://api.fanyi.baidu.com/api/trans/vip/translate";
    const APP_ID = "20250514002356879"; // 替换为您的APPID
    const SEC_KEY = "gwkEnnQJRX6LtfIeT1ea"; // 替换为您的密钥

    /**
     * 翻译入口
     * @param string $query 待翻译的文本
     * @param string $from 源语言
     * @param string $to 目标语言
     * @return array 翻译结果
     */
    public function translate($query, $from, $to)
    {
        $args = [
            'q' => $query,
            'appid' => self::APP_ID,
            'salt' => rand(10000, 99999),
            'from' => $from,
            'to' => $to,
        ];
        $args['sign'] = $this->buildSign($query, self::APP_ID, $args['salt'], self::SEC_KEY);
        $ret = $this->call(self::URL, $args);
        $ret = json_decode($ret, true);
        if(isset($ret['trans_result'][0]['dst'])){
            return $ret['trans_result'][0]['dst'];
        }
        return $ret;
    }

    /**
     * 加密签名
     * @param string $query 待翻译的文本
     * @param string $appID 应用ID
     * @param int $salt 随机盐值
     * @param string $secKey 密钥
     * @return string 签名结果
     */
    private function buildSign($query, $appID, $salt, $secKey)
    {
        $str = $appID . $query . $salt . $secKey;
        return md5($str);
    }

    /**
     * 发起网络请求
     * @param string $url 请求地址
     * @param array|null $args 请求参数
     * @param string $method 请求方法
     * @param int $timeout 超时时间
     * @param array $headers 请求头
     * @return mixed 响应结果
     */
    private function call($url, $args = null, $method = "post", $timeout = self::CURL_TIMEOUT, $headers = [])
    {
        $ret = false;
        $i = 0;
        while ($ret === false) {
            if ($i > 1) {
                break;
            }
            if ($i > 0) {
                sleep(1);
            }
            $ret = $this->callOnce($url, $args, $method, $timeout, $headers);
            $i++;
        }
        return $ret;
    }

    /**
     * 单次网络请求
     * @param string $url 请求地址
     * @param array|null $args 请求参数
     * @param string $method 请求方法
     * @param int $timeout 超时时间
     * @param array $headers 请求头
     * @return mixed 响应结果
     */
    private function callOnce($url, $args = null, $method = "post", $timeout = self::CURL_TIMEOUT, $headers = [])
    {
        $ch = curl_init();
        if ($method == "post") {
            $data = $this->convert($args);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_POST, 1);
        } else {
            $data = $this->convert($args);
            if ($data) {
                if (stripos($url, "?") > 0) {
                    $url .= "&$data";
                } else {
                    $url .= "?$data";
                }
            }
        }
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        if (!empty($headers)) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        }
        $r = curl_exec($ch);
        curl_close($ch);
        return $r;
    }

    /**
     * 转换参数为URL格式
     * @param array $args 参数数组
     * @return string 转换后的字符串
     */
    private function convert(&$args)
    {
        $data = '';
        if (is_array($args)) {
            foreach ($args as $key => $val) {
                if (is_array($val)) {
                    foreach ($val as $k => $v) {
                        $data .= $key . '[' . $k . ']=' . rawurlencode($v) . '&';
                    }
                } else {
                    $data .= "$key=" . rawurlencode($val) . "&";
                }
            }
            return trim($data, "&");
        }
        return $args;
    }
}