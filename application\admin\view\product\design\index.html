<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            <li class="{:$Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="statusList" item="vo"}
            <li class="{:$Think.get.status === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('product/design/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>

                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('product/design/edit')}"
                           data-operate-del="{:$auth->check('product/design/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<style>
    .design-card-container {
        display: flex;
        flex-direction: column;
        margin: 0;
    }
    .design-card {
        width: 100%;
        margin: 10px 0;
        border: 1px solid #ccc;
        background-color: #f5f5f5;
        display: flex;
        position: relative;
    }
    .design-image {
        width: 240px;
        height: 240px;
        padding: 10px;
        background-color: #fff;
        border-right: 1px solid #ccc;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .design-image img {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        object-fit: contain;
    }
    .design-info {
        flex: 1;
        padding: 10px 15px;
        font-size: 14px;
        color: #333;
    }
    .design-info-row {
        margin-bottom: 5px;
        line-height: 1.5;
    }
    .info-label {
        display: inline-block;
        width: 80px;
        font-weight: bold;
    }
    .info-value {
        display: inline-block;
    }
    .design-actions {
        position: absolute;
        right: 10px;
        top: 20px;
        display: flex;
        flex-direction: column;
        width: 60px;
    }
    .btn-action {
        display: block;
        width: 60px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        background-color: #f0f0f0;
        border: 1px solid #ccc;

        color: #333;
        text-decoration: none;
        padding: 0;
        margin: 0;
        margin-bottom: 10px;
    }
    .btn-action:hover {
        background-color: #e0e0e0;
        color: #333;
        text-decoration: none;
    }
    .btn-edit {
        border-top: 1px solid #ccc;
    }

    @media (max-width: 767px) {
        .design-card {
            flex-direction: column;
        }
        .design-image {
            width: 100%;
            height: auto;
            border-right: none;
            border-bottom: 1px solid #ccc;
        }
        .design-info {
            width: 100%;
            padding-right: 70px;
        }
        .design-actions {
            top: auto;
            bottom: 0;
            height: 100%;
        }
    }
</style>
