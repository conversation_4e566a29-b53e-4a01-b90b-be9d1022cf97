<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 店铺管理
 *
 * @icon fa fa-circle-o
 */
class Store extends Backend
{

    /**
     * Store模型对象
     * @var \app\admin\model\Store
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Store;

    }


    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 使用field明确指定要查询的字段，避免字段名冲突
            $list = $this->model
                    ->field('*')  // 明确指定使用店铺表的所有字段
                    ->with(['latestMoneyLog'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);
            // 将模型集合转换为数组
            $items = collection($list->items())->toArray();
            $result = array("total" => $list->total(), "rows" => $items);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取店铺打款记录列表
     */
    public function getStoreMoneyLogList()
    {
        $store_id = $this->request->param('store_id', 0);
        if (!$store_id) {
            $this->error(__('Parameter %s can not be empty', 'store_id'));
        }

        $store = $this->model->get($store_id);
        if (!$store) {
            $this->error(__('Store not found'));
        }

        $this->redirect('store_money_log/index', ['store_id' => $store_id]);
    }
}
