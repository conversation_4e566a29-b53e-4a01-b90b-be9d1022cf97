<?php

namespace app\admin\controller\product;

use app\common\controller\Backend;
use think\Db;
/**
 * 在售产品
 *
 * @icon fa fa-circle-o
 */
class Sale extends Backend
{

    /**
     * Sale模型对象
     * @var \app\admin\model\product\Sale
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\product\Sale;
		$this->modelordergoods = new \app\admin\model\Ordergoods;
        $this->view->assign("stylelistList", $this->model->getStylelistList());
		$this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['admin','design','store','designer'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {

                $row->getRelation('admin')->visible(['nickname']);
				$row->getRelation('design')->visible(['name','store_id']);
				$row->getRelation('store')->visible(['name']);
				$row->getRelation('designer')->visible(['name']);
				$num = $this->modelordergoods->where('product_sale_id',$row['id'])->count();
				$this->model->where('id',$row['id'])->update(['salenum'=>$num]);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
		$siteconfig = Db::table('fa_config')->where('name','jlnum')->find();
		$this->assignconfig('jlnum', $siteconfig['value']);
        return $this->view->fetch();
    }

	/**
	 * 添加
	 *
	 * @return string
	 * @throws \think\Exception
	 */
	public function add()
	{
	    if (false === $this->request->isPost()) {
	        // 获取URL中的ids参数（从设计模块传递过来的）
	        $design_id = $this->request->param('ids');

	        // 如果有设计ID，则从数据库中获取相应的设计信息
	        if ($design_id) {
	            $design_info = Db::table('fa_product_design')->where('id', $design_id)->find();
	            if ($design_info) {
	                // 将设计信息传递给视图
	                $this->view->assign('design_info', $design_info);
	                // 预设表单中的一些字段
	                $this->view->assign('product_design_id', $design_id);
	                $this->view->assign('name', $design_info['name']);
	                $this->view->assign('images', $design_info['images']);
	                $this->view->assign('store_id', $design_info['store_id']);
	            }
	        }

	        return $this->view->fetch();
	    }
	    $params = $this->request->post('row/a');
	    if (empty($params)) {
	        $this->error(__('Parameter %s can not be empty', ''));
	    }
	    $params = $this->preExcludeFields($params);

	    if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
	        $params[$this->dataLimitField] = $this->auth->id;
	    }
	    $result = false;
	    Db::startTrans();
	    try {
	        //是否采用模型验证
	        if ($this->modelValidate) {
	            $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
	            $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
	            $this->model->validateFailException()->validate($validate);
	        }
			$params['admin_id'] = $this->auth->id;
			$product_design_row = Db::table('fa_product_design')->where('id',$params['product_design_id'])->find();
			$params['designer_id'] = $product_design_row ? $product_design_row['designer_id'] : '';

			// 如果用户没有选择店铺，则使用设计表中的店铺ID
			if (empty($params['store_id']) && $product_design_row) {
				$params['store_id'] = $product_design_row['store_id'];
			}

			Db::table('fa_product_design')->where('id',$params['product_design_id'])->update(['status'=>1,'updatetime'=>time()]);
	        $result = $this->model->allowField(true)->save($params);
	        Db::commit();
	    } catch (ValidateException|PDOException|Exception $e) {
	        Db::rollback();
	        $this->error($e->getMessage());
	    }
	    if ($result === false) {
	        $this->error(__('No rows were inserted'));
	    }
	    $this->success();
	}

	/**
	 * 编辑
	 *
	 * @param $ids
	 * @return string
	 * @throws DbException
	 * @throws \think\Exception
	 */
	public function edit($ids = null)
	{
	    $row = $this->model->get($ids);
	    if (!$row) {
	        $this->error(__('No Results were found'));
	    }
	    $adminIds = $this->getDataLimitAdminIds();
	    if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
	        $this->error(__('You have no permission'));
	    }
	    if (false === $this->request->isPost()) {
	        $this->view->assign('row', $row);
	        return $this->view->fetch();
	    }
	    $params = $this->request->post('row/a');
	    if (empty($params)) {
	        $this->error(__('Parameter %s can not be empty', ''));
	    }
	    $params = $this->preExcludeFields($params);
	    $result = false;
	    Db::startTrans();
	    try {
	        //是否采用模型验证
	        if ($this->modelValidate) {
	            $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
	            $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
	            $row->validateFailException()->validate($validate);
	        }
			if($row['product_design_id'] != $params['product_design_id']){
				Db::table('fa_product_design')->where('id',$row['product_design_id'])->update(['status'=>0]);
				Db::table('fa_product_design')->where('id',$params['product_design_id'])->update(['status'=>1]);

				$product_design_row = Db::table('fa_product_design')->where('id',$params['product_design_id'])->find();
				$params['designer_id'] = $product_design_row ? $product_design_row['designer_id'] : '';

				// 如果用户没有选择店铺，则使用设计表中的店铺ID
				if (empty($params['store_id']) && $product_design_row) {
					$params['store_id'] = $product_design_row['store_id'];
				}
			}


	        $result = $row->allowField(true)->save($params);
	        Db::commit();
	    } catch (ValidateException|PDOException|Exception $e) {
	        Db::rollback();
	        $this->error($e->getMessage());
	    }
	    if (false === $result) {
	        $this->error(__('No rows were updated'));
	    }
	    $this->success();
	}

	/**
	 * 删除
	 *
	 * @param $ids
	 * @return void
	 * @throws DbException
	 * @throws DataNotFoundException
	 * @throws ModelNotFoundException
	 */
	public function del($ids = null)
	{
	    if (false === $this->request->isPost()) {
	        $this->error(__("Invalid parameters"));
	    }
	    $ids = $ids ?: $this->request->post("ids");
	    if (empty($ids)) {
	        $this->error(__('Parameter %s can not be empty', 'ids'));
	    }
	    $pk = $this->model->getPk();
	    $adminIds = $this->getDataLimitAdminIds();
	    if (is_array($adminIds)) {
	        $this->model->where($this->dataLimitField, 'in', $adminIds);
	    }
	    $list = $this->model->where($pk, 'in', $ids)->select();

	    $count = 0;
	    Db::startTrans();
	    try {
	        foreach ($list as $item) {
	            $count += $item->delete();
	        }
	        Db::commit();
	    } catch (PDOException|Exception $e) {
	        Db::rollback();
	        $this->error($e->getMessage());
	    }
	    if ($count) {
	        $this->success();
	    }
	    $this->error(__('No rows were deleted'));
	}

	public function jiangli($ids = null)
	{
		$row = $this->model->get($ids);
		if (!$row) {
		    $this->error(__('No Results were found'));
		}
		if ($this->request->isAjax()) {
			$result = $this->model->where('id',$ids)->update(['status' => 1]);
			if($result){
				$this->success("操作成功", null, ['id' => $ids]);
			}
			$this->error(__('No rows were deleted'));
		}
	}

}
