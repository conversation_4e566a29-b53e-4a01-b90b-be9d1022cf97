<?php

namespace app\admin\model;

use think\Model;


class Kesu extends Model
{

    

    

    // 表名
    protected $name = 'kesu';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'jltime_text',
        'opetype_text',
        'gjlist_text',
        'status_text'
    ];
    

    
    public function getOpetypeList()
    {
        return ['0' => __('Opetype 0'), '1' => __('Opetype 1')];
    }

    public function getGjlistList()
    {
        return ['0' => __('Gjlist 0'), '1' => __('Gjlist 1'), '2' => __('Gjlist 2')];
    }

    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1')];
    }


    public function getJltimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['jltime']) ? $data['jltime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getOpetypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['opetype']) ? $data['opetype'] : '');
        $list = $this->getOpetypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getGjlistTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['gjlist']) ? $data['gjlist'] : '');
        $list = $this->getGjlistList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setJltimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function admin()
    {
        return $this->belongsTo('Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function store()
    {
        return $this->belongsTo('Store', 'store_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function order()
    {
        return $this->belongsTo('Order', 'order_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
	
	public function kesulog()
	{
	    return $this->belongsTo('Kesulog', 'kesulog_id', 'id', [], 'LEFT')->setEagerlyType(0);
	}
}
